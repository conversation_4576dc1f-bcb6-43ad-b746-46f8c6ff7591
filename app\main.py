from fastapi import <PERSON><PERSON><PERSON>, HTT<PERSON>Exception, Request, Body
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
import datetime
import os
import asyncio
import time
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor
from app.utils.post_creator import generate_scheduled_post_content, add_emojis_to_post, generate_post_from_persona_keywords, fetch_related_url
from app.utils.post_management import generate_post_schedule, get_post_insights, generate_persona_posts_from_keywords
from app.utils.ai_content import generate_content_ideas, generate_content_categories, _generate_related_categories
from app.utils.hashtag_generator import generate_hashtags, fetch_popular_hashtags
from app.utils.content_modifier import modify_content
from app.utils.profile_scoring import score_profile

from app.utils.agentic_trending_news import get_trending_news_agentic


from app.utils.general_persona_generator import generate_general_persona
from app.utils.prompt_templates_new import TEMPLATES
import json
import re
import random
from app.utils.model_initializer import model


app = FastAPI(
    title="LinkedIn Content Creation API",
    description="API for generating and managing LinkedIn content using Vertex AI and Gemini 2.0 Flash",
    version="1.0.0"
)

# Add CORS middleware for better performance
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Request models
class WorkExperience(BaseModel):
    company: str
    location: str
    position: str
    startDate: str
    endDate: str
    industry: str
    job_description: str

class Education(BaseModel):
    degree: Optional[str] = None
    school_name: Optional[str] = None
    years_of_study: Optional[str] = None

class SchedulePostRequest(BaseModel):
    general_persona_keywords: list[str]
    content_persona_keywords: list[str] = None
    network_persona_keywords: list[str] = None
    tone: str = "Professional"
    start_date: str
    end_date: str
    categories: list[str]
    add_emojis: bool = True  # Enable emojis by default for scheduled posts
    add_hashtags: bool = True  # Enable hashtags by default for scheduled posts

# Additional Request Models
class ContentIdeasRequest(BaseModel):
    industry: str
    target_audience: str
    content_focus: str = None
    style: str = "Informative"

class HashtagRequest(BaseModel):
    post_content: str
    interests: list[str] = []

class ContentModificationRequest(BaseModel):
    original_content: str
    tone: str = None  # Optional
    style: str = None  # Optional
    length: str = None  # Optional
    add_emojis: bool = False  # Flag to add emojis to the post
    add_hashtags: bool = False  # Flag to add hashtags to the post

class PostSuggestionRequest(BaseModel):
    general_persona_keywords: list[str]
    content_persona_keywords: list[str] = None
    network_persona_keywords: list[str] = None
    tone: str = "Professional"
    style: str = "Informative"
    user_prompt: str = None
    length: str = "medium"

class PostInsightsRequest(BaseModel):
    industry: str
    target_audience: str

class EmojiRequest(BaseModel):
    post_content: str

class TrendCheckRequest(BaseModel):
    post_id: int
    date: str
    time: str
    content: str

class UserProfile(BaseModel):
    name: str
    headline: str
    summary: str
    work_experience: List[WorkExperience]
    current_role: str
    organizations: str
    industry: str
    city: str
    country: str
    skills: List[str]
    certifications: List[str]
    languages: List[str]
    education: List[Education]

class ContentSection(BaseModel):
    data: List[Any] = []
    content_interests: List[str] = []

class NetworkSection(BaseModel):
    data: List[Any] = []
    network_interests: List[str] = []

class GeneralPersonaRequest(BaseModel):
    user_profile: UserProfile
    content: ContentSection = ContentSection()
    network: NetworkSection = NetworkSection()

class PostRequest(BaseModel):
    general_persona_keywords: list[str]
    content_persona_keywords: list[str] = None
    network_persona_keywords: list[str] = None
    tone: str = "Professional"
    style: str = "Informative"
    length: str = "medium"  # Add missing length parameter with default value
    user_prompt: str = None
    post_categories: list[str] = None  # Optional list of post categories
    post_urls: list[str] = None  # Optional list of post URLs
    add_emojis: bool = True  # Flag to add emojis to the post
    add_hashtags: bool = True  # Flag to add hashtags to the post
    content: str = None  # New optional field for user-provided content

class GeneratePromptRequest(BaseModel):
    general_persona_keywords: list[str]
    content_persona_keywords: list[str] = None
    network_persona_keywords: list[str] = None

class GenerateUrlRequest(BaseModel):
    content: str

class GenerateContentCategoriesRequest(BaseModel):
    general_persona_keywords: list[str]
    content_persona_keywords: list[str] = None
    network_persona_keywords: list[str] = None
    categories: list[str] = None

class TrendingNewsRequest(BaseModel):
    general_persona_keywords: list[str]
    content_persona_keywords: list[str] = None
    network_persona_keywords: list[str] = None
    categories: list[str] = None  # Optional list of categories

class PopularHashtagsRequest(BaseModel):
    general_persona_keywords: list[str]
    content_persona_keywords: list[str] = None
    network_persona_keywords: list[str] = None

class FeedPostItem(BaseModel):
    activity_urn: str
    text: str
    total_reactions: int = 0
    total_comments: int = 0
    total_shares: int = 0
    author_urn: str

class TodaysFeedRequest(BaseModel):
    general_persona_keywords: list[str]
    network_persona_keywords: list[str] = None
    content_persona_keywords: list[str] = None
    posts: list[FeedPostItem]

class SuggestCommentRequest(BaseModel):
    general_persona_keywords: list[str]
    content_persona_keywords: list[str] = None
    network_persona_keywords: list[str] = None
    content: str


# Performance monitoring
_request_times = {}

def log_request_time(endpoint: str, start_time: float):
    """Log request processing time for performance monitoring."""
    processing_time = time.time() - start_time
    if endpoint not in _request_times:
        _request_times[endpoint] = []
    _request_times[endpoint].append(processing_time)
    
    # Keep only last 100 requests for each endpoint
    if len(_request_times[endpoint]) > 100:
        _request_times[endpoint] = _request_times[endpoint][-100:]

# API Endpoints

@app.get("/health")
def health_check():
    """Health check endpoint for GCP deployment monitoring.
    This endpoint doesn't depend on Vertex AI initialization.
    """
    import sys
    import platform

    # Get environment information
    env_vars = {k: v for k, v in os.environ.items() if k in [
        'PROJECT_ID', 'LOCATION', 'MODEL_NAME', 'K_SERVICE',
        'K_REVISION', 'K_CONFIGURATION', 'PORT', 'PYTHONPATH'
    ]}

    # Get current directory and contents
    try:
        current_dir = os.getcwd()
        dir_contents = os.listdir(current_dir)
        app_dir_contents = os.listdir(os.path.join(current_dir, 'app')) if os.path.exists(os.path.join(current_dir, 'app')) else []
    except Exception as e:
        current_dir = str(e)
        dir_contents = []
        app_dir_contents = []

    # Return detailed health information
    return {
        "status": "healthy",
        "timestamp": datetime.datetime.now().isoformat(),
        "environment": "production" if os.environ.get('K_SERVICE') else "development",
        "python_version": sys.version,
        "platform": platform.platform(),
        "environment_variables": env_vars,
        "current_directory": current_dir,
        "directory_contents": dir_contents[:10],  # Limit to first 10 items
        "app_directory_contents": app_dir_contents[:10]  # Limit to first 10 items
    }

@app.get("/performance")
def get_performance_stats():
    """Get performance statistics for all endpoints."""
    stats = {}
    for endpoint, times in _request_times.items():
        if times:
            stats[endpoint] = {
                "avg_response_time": sum(times) / len(times),
                "min_response_time": min(times),
                "max_response_time": max(times),
                "total_requests": len(times)
            }
    return stats

@app.post("/create-post")
async def create_post(request: PostRequest):
    """Generate LinkedIn posts based on general persona keywords or rewrite user content."""
    start_time = time.time()
    try:
        from app.utils.model_initializer import model
        model.clear_cache()
        persona_keywords = request.general_persona_keywords
        content_keywords = request.content_persona_keywords or []
        network_keywords = request.network_persona_keywords or []

        # --- NEW: Handle user-provided content ---
        if request.content and request.content.strip():
            print(f"Processing user-provided content: {len(request.content)} characters")
            print(f"Content preview: {request.content[:200]}...")

            # Create a proper prompt for content rewriting
            content_prompt = f"As a {', '.join(persona_keywords)}, rewrite and enhance this content for LinkedIn: {request.content}"
            if content_keywords:
                content_prompt += f". Focus on these topics: {', '.join(content_keywords)}"
            if network_keywords:
                content_prompt += f". Target audience: {', '.join(network_keywords)}"

            print(f"Generated content prompt: {content_prompt[:300]}...")

            post = generate_post_from_persona_keywords(
                persona_keywords,
                request.tone,
                request.style,
                content_prompt,  # Use enhanced content prompt
                length=request.length,
                content_interests=content_keywords,
                network_interests=network_keywords,
                add_emojis=request.add_emojis,
                add_hashtags=request.add_hashtags,
                use_hook_generator=True
            )

            # Debug: Log the post generation result
            print(f"Post generation result type: {type(post)}")
            if isinstance(post, dict):
                print(f"Post result keys: {list(post.keys())}")
                if "posts" in post:
                    print(f"Number of posts generated: {len(post['posts'])}")

            # Handle different response formats from generate_post_from_persona_keywords
            final_content = ""
            has_image = False
            has_infographics = False
            url = None

            try:
                if post and isinstance(post, dict) and "posts" in post and len(post["posts"]) > 0:
                    single_post = post["posts"][0]
                    final_content = single_post.get("content", "")
                    print(f"Extracted content length: {len(final_content)} characters")

                    # Analyze the content for media recommendations
                    from app.utils.post_creator import analyze_post_for_media
                    media = analyze_post_for_media(final_content)
                    has_image = media.get("has_image", False)
                    has_infographics = media.get("has_infographics", False)
                    url = single_post.get("has_url", None)

                elif post and isinstance(post, list) and len(post) > 0:
                    # Handle case where function returns a list directly
                    single_post = post[0]
                    final_content = single_post.get("content", "") if isinstance(single_post, dict) else str(single_post)
                    print(f"Extracted content from list format: {len(final_content)} characters")

                elif post and isinstance(post, str):
                    # Handle case where function returns a string directly
                    final_content = post
                    print(f"Extracted content from string format: {len(final_content)} characters")

                else:
                    print(f"Warning: Unexpected post format or empty result: {post}")
                    final_content = f"Enhanced version of: {request.content[:100]}... [Content processing failed, please try again]"

            except Exception as e:
                print(f"Error processing content result: {e}")
                final_content = f"Enhanced version of: {request.content[:100]}... [Content processing error: {str(e)}]"

            # Ensure we have some content
            if not final_content or not final_content.strip():
                print("Warning: Final content is empty, using fallback")
                final_content = f"Here's an enhanced version of the content: {request.content}"

            result = {
                "posts": [{
                    "content": final_content,
                    "has_image": has_image,
                    "has_infographics": has_infographics,
                    "url": url
                }]
            }
            log_request_time("/create-post", start_time)
            return result
        # --- END NEW ---

        # Priority 1: Categories
        if request.post_categories and len(request.post_categories) > 0:
            categories = []
            for category in request.post_categories:
                if "," in category:
                    categories.extend([c.strip() for c in category.split(",")])
                else:
                    categories.append(category.strip())
            categories = list(filter(None, categories))  # Remove empty categories but keep duplicates

            if categories:
                posts = []
                async def generate_category_post(category, index):
                    # Create dynamic, flexible prompts for any category
                    if request.user_prompt:
                        # If user provides a custom prompt, incorporate the category into it
                        prompt = f"As a {', '.join(persona_keywords)}, {request.user_prompt}. Focus specifically on the topic of '{category}'"
                    else:
                        # Create a dynamic prompt that works for any category
                        prompt = f"As a {', '.join(persona_keywords)}, write a LinkedIn post about '{category}'. Make the content relevant, engaging, and authentic to your professional perspective"

                    # Add content interests if provided
                    if content_keywords:
                        prompt = f"{prompt}. Incorporate these topics where relevant: {', '.join(content_keywords)}"

                    # Add network targeting if provided
                    if network_keywords:
                        prompt = f"{prompt}. Target audience: {', '.join(network_keywords)}"
                    post = generate_post_from_persona_keywords(
                        persona_keywords,
                        request.tone,
                        category,  # Use category as style
                        prompt,
                        length=request.length,
                        content_interests=content_keywords,
                        network_interests=network_keywords,
                        add_emojis=request.add_emojis,
                        add_hashtags=request.add_hashtags,
                        use_hook_generator=True
                    )
                    # Handle multiple possible return formats from generator
                    post_content = None
                    if post:
                        if isinstance(post, dict) and "posts" in post and len(post["posts"]) > 0:
                            single_post = post["posts"][0]
                            post_content = single_post.get("content", "")
                        elif isinstance(post, list) and len(post) > 0:
                            first_item = post[0]
                            if isinstance(first_item, dict):
                                post_content = first_item.get("content", "")
                            else:
                                post_content = str(first_item)
                        elif isinstance(post, str):
                            post_content = post
                    if post_content:

                        # Clean up any markdown formatting
                        if "```html" in post_content:
                            post_content = post_content.split("```html")[1].split("```")[0].strip()
                        elif "```" in post_content:
                            post_content = post_content.split("```")[1].strip()

                        # Analyze the final content for media recommendations
                        from app.utils.post_creator import analyze_post_for_media
                        media_analysis = analyze_post_for_media(post_content)
                        has_image = media_analysis.get("has_image", False)
                        has_infographics = media_analysis.get("has_infographics", False)

                        return {
                            "category": category,
                            "content": post_content,
                            "has_image": has_image,
                            "has_infographics": has_infographics,
                            "index": index
                        }
                    return None
                tasks = [generate_category_post(category, i) for i, category in enumerate(categories)]
                results = await asyncio.gather(*tasks, return_exceptions=True)
                for result in results:
                    if isinstance(result, dict) and result.get("category"):
                        posts.append(result)
                if posts:
                    # Sort by index to maintain original order
                    posts.sort(key=lambda x: x.get("index", 0))
                    # Remove index from final output
                    for post in posts:
                        post.pop("index", None)
                    result = {"posts": posts}
                else:
                    fallback_result = generate_post_from_persona_keywords(
                        persona_keywords,
                        request.tone,
                        request.style,
                        request.user_prompt or "",
                        length=request.length,
                        content_interests=content_keywords,
                        network_interests=network_keywords,
                        add_emojis=request.add_emojis,
                        add_hashtags=request.add_hashtags,
                        use_hook_generator=True
                    )
                    # Wrap fallback result in consistent format
                    if isinstance(fallback_result, list) and len(fallback_result) > 0:
                        result = {"posts": fallback_result}
                    else:
                        result = {"posts": [{"content": str(fallback_result), "has_image": False, "has_infographics": False, "framework": "Unknown", "reason": "Fallback content"}]}
            else:
                fallback_result = generate_post_from_persona_keywords(
                    persona_keywords,
                    request.tone,
                    request.style,
                    request.user_prompt or "",
                    length=request.length,
                    content_interests=content_keywords,
                    network_interests=network_keywords,
                    add_emojis=request.add_emojis,
                    add_hashtags=request.add_hashtags,
                    use_hook_generator=True
                )
                # Wrap fallback result in consistent format
                if isinstance(fallback_result, list) and len(fallback_result) > 0:
                    result = {"posts": fallback_result}
                else:
                    result = {"posts": [{"content": str(fallback_result), "has_image": False, "has_infographics": False, "framework": "Unknown", "reason": "Fallback content"}]}
        # Priority 2: URLs
        elif request.post_urls and len(request.post_urls) > 0:
            urls = list(set(filter(None, [u.strip() for u in request.post_urls if u.strip()])) )
            if urls:
                posts = []
                import requests as _requests
                from bs4 import BeautifulSoup
                async def fetch_url_content(url):
                    try:
                        print(f"Fetching content from URL: {url}")
                        headers = {
                            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
                        }
                        resp = _requests.get(url, timeout=15, headers=headers)
                        resp.raise_for_status()  # Raise an exception for bad status codes

                        soup = BeautifulSoup(resp.text, "html.parser")

                        # Remove script and style elements
                        for script in soup(["script", "style"]):
                            script.decompose()

                        # Try to get meaningful content
                        content_parts = []

                        # Get title
                        title = soup.find('title')
                        if title:
                            content_parts.append(f"Title: {title.get_text().strip()}")

                        # Get meta description
                        meta_desc = soup.find('meta', attrs={'name': 'description'})
                        if meta_desc and meta_desc.get('content'):
                            content_parts.append(f"Description: {meta_desc.get('content').strip()}")

                        # Get main content
                        paragraphs = [p.get_text().strip() for p in soup.find_all('p') if p.get_text().strip()]
                        if paragraphs:
                            content_parts.extend(paragraphs[:5])  # Limit to first 5 paragraphs

                        # If no paragraphs, try headings
                        if not paragraphs:
                            headings = [h.get_text().strip() for h in soup.find_all(['h1', 'h2', 'h3']) if h.get_text().strip()]
                            content_parts.extend(headings[:3])

                        # If still no content, get all text
                        if not content_parts:
                            all_text = soup.get_text()
                            # Clean up whitespace
                            cleaned_text = ' '.join(all_text.split())
                            if cleaned_text:
                                content_parts.append(cleaned_text[:1000])

                        final_content = '\n'.join(content_parts)
                        print(f"Successfully extracted {len(final_content)} characters from {url}")
                        return final_content.strip()

                    except Exception as e:
                        print(f"Error fetching content from {url}: {str(e)}")
                        return ""
                async def generate_url_post(url):
                    url_content = await fetch_url_content(url)

                    # Debug: Log URL content retrieval
                    print(f"Processing URL: {url}")
                    print(f"Retrieved content length: {len(url_content) if url_content else 0} characters")
                    if url_content:
                        print(f"Content preview: {url_content[:200]}...")
                    else:
                        print("Warning: No content retrieved from URL")

                    # Build URL-specific prompt
                    if url_content and url_content.strip():
                        if request.user_prompt:
                            prompt = f"As a {', '.join(persona_keywords)}, {request.user_prompt}. "
                        else:
                            prompt = f"As a {', '.join(persona_keywords)}, "

                        prompt += f"create a LinkedIn post inspired by this content from {url}: {url_content[:1000]}"

                        if content_keywords:
                            prompt += f". Connect it to these topics: {', '.join(content_keywords)}"
                        if network_keywords:
                            prompt += f". Target audience: {', '.join(network_keywords)}"
                    else:
                        # Fallback if URL content couldn't be retrieved
                        prompt = f"As a {', '.join(persona_keywords)}, write a post about the website {url}"
                        if request.user_prompt:
                            prompt += f". {request.user_prompt}"
                        if content_keywords:
                            prompt += f". Focus on these topics: {', '.join(content_keywords)}"
                        if network_keywords:
                            prompt += f". Target audience: {', '.join(network_keywords)}"
                    post = generate_post_from_persona_keywords(
                        persona_keywords,
                        request.tone,
                        request.style,
                        prompt,
                        length=request.length,
                        content_interests=content_keywords,
                        network_interests=network_keywords,
                        add_emojis=request.add_emojis,
                        add_hashtags=request.add_hashtags,
                        use_hook_generator=True
                    )
                    # Handle multiple possible return formats from generator
                    post_content = None
                    if post:
                        if isinstance(post, dict) and "posts" in post and len(post["posts"]) > 0:
                            single_post = post["posts"][0]
                            post_content = single_post.get("content", "")
                        elif isinstance(post, list) and len(post) > 0:
                            first_item = post[0]
                            if isinstance(first_item, dict):
                                post_content = first_item.get("content", "")
                            else:
                                post_content = str(first_item)
                        elif isinstance(post, str):
                            post_content = post
                    if post_content:

                        # Clean up any markdown formatting
                        if "```html" in post_content:
                            post_content = post_content.split("```html")[1].split("```", 1)[0].strip()
                        elif "```" in post_content:
                            post_content = post_content.split("```", 1)[1].strip()

                        # Analyze the final content for media recommendations
                        from app.utils.post_creator import analyze_post_for_media
                        media_analysis = analyze_post_for_media(post_content)
                        has_image = media_analysis.get("has_image", False)
                        has_infographics = media_analysis.get("has_infographics", False)

                        return {
                            "url": url,
                            "content": post_content,
                            "has_image": has_image,
                            "has_infographics": has_infographics,
                        }
                    return None
                tasks = [generate_url_post(url) for url in urls]
                results = await asyncio.gather(*tasks, return_exceptions=True)
                for result in results:
                    if isinstance(result, dict) and result.get("url"):
                        posts.append(result)
                if posts:
                    result = {"posts": posts}
                else:
                    fallback_result = generate_post_from_persona_keywords(
                        persona_keywords,
                        request.tone,
                        request.style,
                        request.user_prompt or "",
                        length=request.length,
                        content_interests=content_keywords,
                        network_interests=network_keywords,
                        add_emojis=request.add_emojis,
                        add_hashtags=request.add_hashtags,
                        use_hook_generator=True
                    )
                    # Wrap fallback result in consistent format
                    if isinstance(fallback_result, list) and len(fallback_result) > 0:
                        result = {"posts": fallback_result}
                    else:
                        result = {"posts": [{"content": str(fallback_result), "has_image": False, "has_infographics": False, "framework": "Unknown", "reason": "Fallback content"}]}
            else:
                fallback_result = generate_post_from_persona_keywords(
                    persona_keywords,
                    request.tone,
                    request.style,
                    request.user_prompt or "",
                    length=request.length,
                    content_interests=content_keywords,
                    network_interests=network_keywords,
                    add_emojis=request.add_emojis,
                    add_hashtags=request.add_hashtags,
                    use_hook_generator=True
                )
                # Wrap fallback result in consistent formats
                if isinstance(fallback_result, list) and len(fallback_result) > 0:
                    result = {"posts": fallback_result}
                else:
                    result = {"posts": [{"content": str(fallback_result), "has_image": False, "has_infographics": False, "framework": "Unknown", "reason": "Fallback content"}]}
        # Standard mode: 3 variants
        else:
            result = generate_post_from_persona_keywords(
                persona_keywords,
                request.tone,
                request.style,
                request.user_prompt or "",
                length=request.length,
                content_interests=content_keywords,
                network_interests=network_keywords,
                add_emojis=request.add_emojis,
                add_hashtags=request.add_hashtags,
                use_hook_generator=True
            )

            # The generate_post_from_persona_keywords function returns a list of post objects
            # We need to wrap it in the same format as category and URL modes
            if isinstance(result, list) and len(result) > 0:
                # Standard mode: wrap the list in {"posts": [...]} format to match other modes
                posts_data = []
                for post in result:
                    post_data = {
                        "content": post["content"],
                        "has_image": post.get("has_image", False),
                        "has_infographics": post.get("has_infographics", False),
                        "framework": post.get("framework", "Unknown"),
                        "reason": post.get("reason", "Framework selected for optimal content delivery")
                    }
                    if post.get("url"):
                        post_data["url"] = post["url"]
                    posts_data.append(post_data)
                result = {"posts": posts_data}
            elif isinstance(result, dict) and "posts" in result:
                # Already in correct format (shouldn't happen with current implementation)
                result = result
            else:
                # Fallback for unexpected format
                result = {"posts": [{"content": str(result), "has_image": False, "has_infographics": False, "framework": "Unknown", "reason": "Fallback content"}]}
        log_request_time("/create-post", start_time)
        return result
    except Exception as e:
        log_request_time("/create-post", start_time)
        print(f"Error in create_post: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error generating posts: {str(e)}")

@app.post("/schedule-posts")
async def schedule_posts(request: SchedulePostRequest):
    """Schedule posts based on persona keywords, categories, and date range."""
    start_time = time.time()
    try:
        # Create a dictionary with all the persona data
        persona_data = {
            'general_persona_keywords': request.general_persona_keywords,
            'tone': request.tone
        }

        # Add content_persona_keywords if provided
        if request.content_persona_keywords is not None:
            persona_data['content_persona_keywords'] = request.content_persona_keywords

        # Add network_persona_keywords if provided
        if request.network_persona_keywords is not None:
            persona_data['network_persona_keywords'] = request.network_persona_keywords

        # Add categories and emoji/hashtag settings
        persona_data['categories'] = request.categories
        persona_data['add_emojis'] = request.add_emojis
        persona_data['add_hashtags'] = request.add_hashtags

        # Generate schedule using the provided parameters
        schedule = generate_post_schedule(
            persona_data,
            schedule_duration="custom",
            start_date=request.start_date,
            end_date=request.end_date,
            template=None
        )

        # --- Begin post-processing for 20% category-only rule ---
        import random
        num_posts = len(schedule)
        num_category_only = max(1, round(num_posts * 0.2))
        
        # Randomly distribute trending posts across all days regardless of categories
        all_post_indices = list(range(num_posts))
        category_only_indices = set(random.sample(all_post_indices, num_category_only))

        for i, post in enumerate(schedule):
            if i in category_only_indices:
                # Only return category, post_id, scheduled_datetime, and source='trending'
                keys_to_remove = [
                    'style', 'tone', 'length', 'content', 'has_image', 'has_infographics', 'url'
                ]
                for key in keys_to_remove:
                    if key in post:
                        del post[key]
                post['source'] = 'trending'
            else:
                # Fully featured post, add source='ai'
                post['source'] = 'ai'
        # --- End post-processing ---

        # Return the schedule directly since it's already in the correct format
        log_request_time("/schedule-posts", start_time)
        return {"post_schedule": schedule}
    except Exception as e:
        log_request_time("/schedule-posts", start_time)
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/modify-content")
async def modify(request: ContentModificationRequest):
    """
    Modify or enhance LinkedIn post content based on user request.
    This version includes length-aware emoji generation.
    """
    start_time = time.time()
    try:
        if not request.original_content or not request.original_content.strip():
            log_request_time("/modify-content", start_time)
            return {"modified_content": ""}

        # --- 1. Prepare Content and Parameters ---
        hashtag_match = re.search(r"<p class='hashtags'>(.*?)</p>", request.original_content, flags=re.DOTALL)
        existing_hashtags = hashtag_match.group(1).strip().split() if hashtag_match else []
        content_body = re.sub(r"<p class='hashtags'>.*?</p>", "", request.original_content, flags=re.DOTALL).strip()

        is_rewrite_requested = any([
            request.tone not in [None, '', 'string'],
            request.style not in [None, '', 'string'],
            request.length not in [None, '', 'string']
        ])
        
        # Define length here to be accessible for the emoji step
        length = request.length if request.length not in [None, '', 'string'] else 'medium'
        modified_body = ""

        # --- 2. Decide Action: Rewrite or Use Original ---
        if is_rewrite_requested:
            from app.utils.content_modifier import modify_content
            
            tone = request.tone if request.tone not in [None, '', 'string'] else 'Professional'
            style = request.style if request.style not in [None, '', 'string'] else 'Informative'
            
            modified_body = modify_content(
                original_content=content_body,
                tone=tone,
                style=style,
                length=length,
                disable_cache=True
            )
        else:
            modified_body = content_body

        # --- 3. Enhancement Step (now length-aware) ---
        if request.add_emojis:
            from app.utils.content_modifier import get_suggested_emojis, insert_emojis_into_html
            # --- THIS IS THE KEY CHANGE ---
            # Pass the `length` parameter to the emoji function
            suggested_emojis = get_suggested_emojis(modified_body, length=length)
            if suggested_emojis:
                modified_body = insert_emojis_into_html(modified_body, suggested_emojis)

        # --- 4. Hashtag Step ---
        final_content = modified_body
        if request.add_hashtags:
            from app.utils.hashtag_generator import generate_hashtags
            new_hashtags = generate_hashtags(final_content, disable_cache=True, add_extra_hashtags=True).split()
            combined_hashtags = existing_hashtags + [tag for tag in new_hashtags if tag not in existing_hashtags]
            if combined_hashtags:
                hashtags_str = ' '.join(list(dict.fromkeys(combined_hashtags)))
                final_content = f"{final_content}\n\n<p class='hashtags'>{hashtags_str}</p>"
        elif existing_hashtags:
            hashtags_str = ' '.join(existing_hashtags)
            final_content = f"{final_content}\n\n<p class='hashtags'>{hashtags_str}</p>"

        # --- 5. Final Cleanup ---
        from app.utils.html_cleaner import clean_html_content
        cleaned_content = clean_html_content(final_content)
        
        log_request_time("/modify-content", start_time)
        return {"modified_content": cleaned_content}

    except Exception as e:
        log_request_time("/modify-content", start_time)
        print(f"Error in /modify-content endpoint: {str(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"An internal error occurred: {str(e)}")

@app.post("/create-general-persona")
async def create_general_persona(request: GeneralPersonaRequest):
    """Create a general persona based on user profile.

    This endpoint accepts a nested structure with:
    - user_profile: Contains all the user's professional information
    - content: Optional section with data (list of posts) and interests
    - network: Optional section with data (list of connections) and interests

    This endpoint generates:
    - 10-15 general persona keywords that reflect the user's professional identity
    - 8-10 content interests (topics the user would be interested in consuming)
    - 8-10 network interests (types of professionals the user would benefit from connecting with)
    - content_persona (combination of content_interests and unique keywords)
    - network_persona_keywords (combination of network_interests and unique keywords)

    RETURN ANSWER IN THIS " json\n(.*?)\n " JSON FORMAT.
    """
    start_time = time.time()
    # Extract the user profile data
    user_profile = request.user_profile.model_dump()

    # Extract content and network data if available
    content_data = request.content.model_dump() if request.content else {"data": [], "interests": []}
    network_data = request.network.model_dump() if request.network else {"data": [], "interests": []}

    # Combine all data for processing
    request_data = {
        "user_profile": user_profile,
        "content": content_data,
        "network": network_data
    }

    # Generate the persona data
    persona_data = generate_general_persona(request_data)

    # Default values for post parameters
    tone = "Professional"
    style = "Informative"
    length = "medium"

    # Analyze post patterns using Gemini if posts are available
    if content_data.get("data") and len(content_data["data"]) > 0:
        posts = content_data["data"]  # Now directly using the list of posts
        if posts:
            # Create a prompt for Gemini to analyze the posts
            analysis_prompt = f"""Analyze these LinkedIn posts and determine the user's preferred tone, style, and length.
            Posts:
            {json.dumps(posts, indent=2)}

            Please analyze the writing style, tone, and typical length of these posts.
            Return the analysis in this exact JSON format:
            {{
                "tone": "most common tone (Professional/Polite/Witty/Enthusiastic/Friendly/Informational/Funny/Authoritative)",
                "style": "most common style (Informative/Thought-provoking/Personal storytelling)",
                "length": "most common length (short/medium/long)"
            }}
            """

            try:
                # Call Gemini for analysis
                from app.utils.post_creator import generate_post_from_persona_keywords
                analysis_result = generate_post_from_persona_keywords(
                    ["AI Analyst"],  # Using a neutral persona for analysis
                    "Professional",
                    "Informative",
                    analysis_prompt,
                    "medium",
                    [],
                    []
                )

                # Handle the new three-variant return format
                if isinstance(analysis_result, dict) and "posts" in analysis_result and len(analysis_result["posts"]) > 0:
                    # For analysis, use the first post content
                    analysis_result = analysis_result["posts"][0]["content"]

                # Parse the analysis result
                try:
                    analysis = json.loads(analysis_result)
                    tone = analysis.get("tone", tone)
                    style = analysis.get("style", style)
                    length = analysis.get("length", length)
                except json.JSONDecodeError:
                    # If JSON parsing fails, try to extract values using string matching
                    if "tone" in analysis_result.lower():
                        tone = next((t for t in ["Professional", "Polite", "Witty", "Enthusiastic", "Friendly", "Informational", "Funny", "Authoritative"] 
                                   if t.lower() in analysis_result.lower()), tone)
                    if "style" in analysis_result.lower():
                        style = next((s for s in ["Informative", "Thought-provoking", "Personal storytelling"] 
                                    if s.lower() in analysis_result.lower()), style)
                    if "length" in analysis_result.lower():
                        length = next((l for l in ["short", "medium", "long"] 
                                     if l.lower() in analysis_result.lower()), length)
            except Exception as e:
                print(f"Error analyzing posts with Gemini: {str(e)}")

    # Get general persona keywords
    general_keywords = set(persona_data.get("general_persona_keywords", []))

    # Create content_persona structure with unique keywords
    content_interests = set(persona_data.get("content_interests", []))
    content_keywords = content_interests - general_keywords  # Remove any general keywords

    content_persona = {
        "keywords": list(content_keywords),
        "tone": tone,
        "style": style,
        "length": length
    }

    # Create network_persona_keywords with unique keywords
    network_interests = set(persona_data.get("network_interests", []))
    network_keywords = network_interests - general_keywords  # Remove any general keywords

    # Create the response with content and network interests
    response = {
        "general_persona_keywords": list(general_keywords),
        "content_interests": list(content_interests),
        "network_interests": list(network_interests),
        "content_persona": content_persona,
        "network_persona_keywords": list(network_keywords)
    }

    log_request_time("/create-general-persona", start_time)
    return response

@app.post("/suggest-posts")
def generate_prompts(request: GeneratePromptRequest):
    """Generate 4 different prompts and corresponding posts based on persona keywords.
    
    This endpoint generates 4 unique prompts and their corresponding posts based on:
    - Required: general_persona_keywords
    - Optional: content_persona_keywords and network_persona_keywords
    
    Each prompt will be generated with a different focus:
    1. Industry insights and expertise
    2. Personal experience and storytelling
    3. Thought leadership and trends
    4. Professional development and growth
    
    The corresponding posts will be generated in medium length for each prompt.
    
    Returns a list of objects, each with 'prompt' and 'content' keys.
    """
    start_time = time.time()
    try:
        from app.utils.model_initializer import model
        import random
        
        # Create a comprehensive persona description for dynamic prompt generation
        persona_description = f"General Persona: {', '.join(request.general_persona_keywords)}"
        if request.content_persona_keywords:
            persona_description += f". Content Interests: {', '.join(request.content_persona_keywords)}"
        if request.network_persona_keywords:
            persona_description += f". Network Interests: {', '.join(request.network_persona_keywords)}"
        
        # Generate dynamic prompt types using AI instead of hardcoded ones
        def generate_dynamic_prompt_types():
            prompt_types_generation_template = f"""
You are a creative LinkedIn content strategist. Generate 4 unique, punchy prompt types for LinkedIn content.

Persona: {persona_description}

Requirements:
1. Create 4 different, creative content types
2. Keep names SHORT and PUNCHY (1-3 words max)
3. Make focus areas CONCISE and clear
4. Each type should inspire short, impactful content
5. Be specific to the persona's expertise
6. Avoid generic categories - be creative and unique
7. Focus on action-oriented, engaging content

Return ONLY a valid JSON array:
[
  {{
    "name": "Short Punchy Name",
    "focus": "concise focus area",
    "description": "brief description"
  }}
]

Generate exactly 4 unique, punchy prompt types.
"""
            
            temperature = random.uniform(0.9, 1.3)  # Higher temperature for more creativity
            response = model.generate_content(prompt_types_generation_template, use_cache=False, temperature=temperature)
            response_text = response.text.strip()
            
            # Clean up the response text to extract JSON
            if "```json" in response_text:
                start = response_text.find("```json") + 7
                end = response_text.find("```", start)
                if end != -1:
                    response_text = response_text[start:end].strip()
            elif "```" in response_text:
                start = response_text.find("```") + 3
                end = response_text.find("```", start)
                if end != -1:
                    response_text = response_text[start:end].strip()
            
            try:
                import json
                prompt_types = json.loads(response_text)
                # Ensure we have exactly 4 types
                if len(prompt_types) >= 4:
                    return prompt_types[:4]
                else:
                    # Fallback to creative default types if AI generation fails
                    return [
                        {
                            "name": "Behind Scenes",
                            "focus": "revealing insider knowledge and experiences",
                            "description": "Share behind-the-scenes insights and personal stories"
                        },
                        {
                            "name": "Future Vision",
                            "focus": "predicting industry changes and opportunities",
                            "description": "Paint a picture of what's coming next"
                        },
                        {
                            "name": "Challenge Accepted",
                            "focus": "addressing pain points with solutions",
                            "description": "Tackle real challenges with practical solutions"
                        },
                        {
                            "name": "Success Story",
                            "focus": "sharing wins and breakthrough moments",
                            "description": "Inspire others with your journey and lessons"
                        }
                    ]
            except Exception as e:
                print(f"Error parsing prompt types: {e}")
                # Fallback to creative default types
                return [
                    {
                        "name": "Behind Scenes",
                        "focus": "revealing insider knowledge and experiences",
                        "description": "Share behind-the-scenes insights and personal stories"
                    },
                    {
                        "name": "Future Vision",
                        "focus": "predicting industry changes and opportunities",
                        "description": "Paint a picture of what's coming next"
                    },
                    {
                        "name": "Challenge Accepted",
                        "focus": "addressing pain points with solutions",
                        "description": "Tackle real challenges with practical solutions"
                    },
                    {
                        "name": "Success Story",
                        "focus": "sharing wins and breakthrough moments",
                        "description": "Inspire others with your journey and lessons"
                    }
                ]
        
        # Generate dynamic prompt types
        prompt_types = generate_dynamic_prompt_types()
        
        # Log the generated prompt types for debugging
        print(f"Generated creative prompt types:")
        for i, pt in enumerate(prompt_types):
            print(f"  {i+1}. {pt['name']}: {pt['focus']}")
        
        # Generate dynamic prompts using AI for each types
        def generate_dynamic_prompt(prompt_type):
            prompt_generation_template = f"""
You are a creative LinkedIn content strategist. Generate a concise, eye-catching prompt for LinkedIn content.

Persona: {persona_description}

Creative Type: {prompt_type['name']}
Focus: {prompt_type['focus']}

Requirements:
1. Keep it SHORT and CONCISE (max 1 sentence, 15-25 words)
2. Make it engaging and action-oriented
3. Be specific to the persona's expertise
4. Use strong action words
5. Make it feel personal and authentic
6. Focus on the creative type theme
7. Avoid lengthy explanations - be direct and punchy

Examples of concise prompts:
- "Share the biggest lesson you learned from your biggest failure"
- "What's the one thing you wish you knew when you started?"
- "Reveal your most valuable career insight in one sentence"

Generate ONLY the short prompt, no explanations.
"""
            
            # Add some randomness to ensure variety and creativity
            temperature = random.uniform(1.0, 1.4)  # Higher temperature for more creativity
            response = model.generate_content(prompt_generation_template, use_cache=False, temperature=temperature)
            return response.text.strip()
        
        # Generate all prompts concurrently using ThreadPoolExecutor
        with ThreadPoolExecutor(max_workers=4) as executor:
            prompt_futures = [executor.submit(generate_dynamic_prompt, pt) for pt in prompt_types]
            generated_prompts = [future.result() for future in prompt_futures]
        
        # Filter out any exceptions and ensure we have valid prompts
        valid_prompts = []
        for i, prompt in enumerate(generated_prompts):
            if isinstance(prompt, Exception):
                # Fallback to concise creative prompts if AI generation fails
                fallback_prompts = [
                    f"Share your biggest career lesson in one sentence",
                    f"What's the one thing you wish you knew when starting out?",
                    f"Reveal your most valuable insight about {request.general_persona_keywords[0] if request.general_persona_keywords else 'your field'}",
                    f"Share the moment that changed everything for you"
                ]
                valid_prompts.append(fallback_prompts[i % len(fallback_prompts)])
            else:
                valid_prompts.append(prompt)
        
        # Log the generated prompts for debugging
        print(f"Generated creative prompts:")
        for i, prompt in enumerate(valid_prompts):
            print(f"  {i+1}. {prompt[:80]}...")
        
        # Generate corresponding posts for each prompt concurrently
        def generate_post_for_prompt(prompt):
            # Randomly choose between short and medium length
            length = random.choice(["short", "medium"])
            
            # Log the prompt being used for debugging
            print(f"Generating post for prompt: {prompt[:100]}...")

            post = generate_post_from_persona_keywords(
                general_persona_keywords=request.general_persona_keywords,
                tone="Professional",
                style="Informative",
                user_prompt=prompt,
                length=length,  # Randomly choose between short and medium
                content_interests=request.content_persona_keywords,
                network_interests=request.network_persona_keywords,
                add_emojis=True,  # Add emojis to suggest-posts endpoint
                add_hashtags=True,
                use_hook_generator=True
            )

            # Debug: Log the actual return type and structure
            print(f"Post generation returned type: {type(post)}")
            if isinstance(post, list):
                print(f"List length: {len(post)}")
                if len(post) > 0:
                    print(f"First item type: {type(post[0])}")
                    if isinstance(post[0], dict):
                        print(f"First item keys: {list(post[0].keys())}")
            elif isinstance(post, dict):
                print(f"Dict keys: {list(post.keys())}")
            print(f"Post structure: {str(post)[:200]}...")
            # Handle the correct return format from generate_post_from_persona_keywords
            post_content = ""
            try:
                if isinstance(post, list) and len(post) > 0:
                    # Function returns an array of post objects directly
                    if isinstance(post[0], dict) and "content" in post[0]:
                        post_content = post[0]["content"]
                    else:
                        print(f"Warning: First item in list doesn't have content: {post[0]}")
                elif isinstance(post, dict) and "posts" in post and len(post["posts"]) > 0:
                    # Legacy format with 'posts' key
                    if isinstance(post["posts"][0], dict) and "content" in post["posts"][0]:
                        post_content = post["posts"][0]["content"]
                    else:
                        print(f"Warning: First post doesn't have content: {post['posts'][0]}")
                elif isinstance(post, dict) and "content" in post:
                    # Single post object format
                    post_content = post["content"]
                elif isinstance(post, str):
                    # Plain string format
                    post_content = post
                else:
                    print(f"Warning: Unexpected post format: {type(post)}")
                    print(f"Post data: {str(post)[:200]}...")

                # Ensure we have some content
                if not post_content or not post_content.strip():
                    print("Warning: Post content is empty, using fallback")
                    post_content = f"Here's a professional insight about {prompt[:50]}... [Content generation failed, please try again]"

            except Exception as e:
                print(f"Error extracting post content: {e}")
                post_content = f"Here's a professional insight about {prompt[:50]}... [Content extraction failed, please try again]"

            # Remove unwanted markdown formatting (like asterisks) from post_content
            post_content = re.sub(r"\*+", "", post_content)
            post_content = re.sub(r"`+", "", post_content)
            post_content = post_content.replace("**", "").replace("*", "")
            post_content = post_content.strip()

            # Remove unwanted markdown formatting (like asterisks) from prompt as well
            clean_prompt = re.sub(r"\*+", "", prompt)
            clean_prompt = re.sub(r"`+", "", clean_prompt)
            clean_prompt = clean_prompt.replace("**", "").replace("*", "")
            clean_prompt = clean_prompt.strip()

            # Log a brief summary of the generated post for validation
            print(f"Generated post length: {len(post_content)} characters")
            
            # Format prompt as HTML
            html_prompt = f"<p>{clean_prompt}</p>"
            return {"prompt": html_prompt, "content": post_content}
        
        # Generate posts concurrently using ThreadPoolExecutor
        with ThreadPoolExecutor(max_workers=4) as executor:
            post_futures = [executor.submit(generate_post_for_prompt, prompt) for prompt in valid_prompts]
            response = []

            for i, future in enumerate(post_futures):
                try:
                    result = future.result()
                    if result and isinstance(result, dict) and "content" in result and result["content"].strip():
                        response.append(result)
                    else:
                        print(f"Warning: Invalid result for prompt {i+1}: {result}")
                        # Add fallback response with HTML-formatted prompt
                        clean_fallback_prompt = re.sub(r"\*+", "", valid_prompts[i])
                        clean_fallback_prompt = re.sub(r"`+", "", clean_fallback_prompt)
                        clean_fallback_prompt = clean_fallback_prompt.replace("**", "").replace("*", "")
                        clean_fallback_prompt = clean_fallback_prompt.strip()
                        response.append({
                            "prompt": f"<p>{clean_fallback_prompt}</p>",
                            "content": f"Here's a professional insight about {valid_prompts[i][:50]}... [Content generation failed, please try again]"
                        })
                except Exception as e:
                    print(f"Error generating post for prompt {i+1}: {e}")
                    # Add fallback response with HTML-formatted prompt
                    clean_fallback_prompt = re.sub(r"\*+", "", valid_prompts[i])
                    clean_fallback_prompt = re.sub(r"`+", "", clean_fallback_prompt)
                    clean_fallback_prompt = clean_fallback_prompt.replace("**", "").replace("*", "")
                    clean_fallback_prompt = clean_fallback_prompt.strip()
                    response.append({
                        "prompt": f"<p>{clean_fallback_prompt}</p>",
                        "content": f"Here's a professional insight about {valid_prompts[i][:50]}... [Content generation failed, please try again]"
                    })

        # Ensure we have at least some responses
        if not response:
            print("Warning: No posts generated, using fallback responses")
            response = [
                {
                    "prompt": "<p>Share your biggest career lesson</p>",
                    "content": "Every challenge in your career is an opportunity to grow. The key is to approach each obstacle with curiosity rather than fear, and to always ask: 'What can I learn from this?' This mindset has helped me turn setbacks into stepping stones throughout my professional journey."
                }
            ]
        
        log_request_time("/suggest-posts", start_time)
        return response
        
    except Exception as e:
        log_request_time("/suggest-posts", start_time)
        return {"error": str(e)}

@app.post("/generate-url")
async def generate_url(request: GenerateUrlRequest):
    """Generate a related URL based on the provided content using SERP API.
    
    This endpoint takes content as input and returns a relevant URL that could be
    used as a reference or source for the content.
    
    Args:
        request (GenerateUrlRequest): The request containing the content
        
    Returns:
        dict: A dictionary containing only the generated URL
    """
    start_time = time.time()
    try:
        # Use the existing fetch_related_url function to get a relevant URL
        generated_url = fetch_related_url(request.content)
        
        if not generated_url:
            raise HTTPException(status_code=404, detail="No relevant URL found for the given content")
        
        log_request_time("/generate-url", start_time)
        return {"url": generated_url}
        
    except Exception as e:
        log_request_time("/generate-url", start_time)
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/generate-content-categories")
async def generate_content_categories_endpoint(request: GenerateContentCategoriesRequest):
    """
    Generate unique, eye-catching content categories based on persona keywords.
    When categories are provided, generates 3 related categories for each input category.
    When categories are empty/null, generates 10 categories based on persona keywords.
    All arguments except general_persona_keywords are optional.
    
    Features:
    - Categories are unique (no duplicates)
    - Categories are eye-catching and compelling (1-2 words max)
    - Categories reflect the specific context of the persona
    - Uses action words, trending terms, and industry buzzwords
    
    Returns: {"categories": [list of {name, description}]}
    """
    try:
        if request.categories and len(request.categories) > 0:
            all_related = []
            global_seen_names = set()
            max_attempts = 5  # Prevent infinite loops
            for category in request.categories:
                unique_related = []
                local_seen = set()
                attempts = 0
                while len(unique_related) < 3 and attempts < max_attempts:
                    related_categories = _generate_related_categories(
                        category, 
                        request.general_persona_keywords, 
                        request.content_persona_keywords, 
                        request.network_persona_keywords
                    )
                    for cat in related_categories:
                        name_key = cat["name"].strip().lower()
                        if name_key not in local_seen and name_key not in global_seen_names:
                            local_seen.add(name_key)
                            global_seen_names.add(name_key)
                            unique_related.append(cat)
                        if len(unique_related) == 3:
                            break
                    attempts += 1
                all_related.extend(unique_related)
            return {"categories": all_related}
        else:
            categories = generate_content_categories(
                general_persona_keywords=request.general_persona_keywords,
                content_persona_keywords=request.content_persona_keywords,
                network_persona_keywords=request.network_persona_keywords,
                categories=request.categories
            )
            return {"categories": categories}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error generating categories: {str(e)}")

@app.post("/trending-news")
async def get_trending_news(request: TrendingNewsRequest):
    """
    Get trending news using agentic architecture with Supervisor Agent coordination.
    Returns news grouped by categories with up to 5 recent articles per category.
    """
    start_time = time.time()
    try:
        # Use the new agentic architecture
        result = await get_trending_news_agentic(
            general_persona_keywords=request.general_persona_keywords,
            content_persona_keywords=request.content_persona_keywords,
            network_persona_keywords=request.network_persona_keywords,
            categories=request.categories
        )
        
        log_request_time("/trending-news", start_time)
        return result
        
    except Exception as e:
        log_request_time("/trending-news", start_time)
        raise HTTPException(status_code=500, detail=f"Error fetching trending news: {str(e)}")

@app.post("/popular-posts-hashtags")
async def get_popular_hashtags(request: PopularHashtagsRequest):
    """
    Get popular hashtags for persona-related and recent content using Serp API.
    
    This endpoint fetches 8 hashtags total:
    - 4 persona-related hashtags based on the provided keywords
    - 4 recent/trending hashtags
    
    Args:
        request (PopularHashtagsRequest): The request containing persona keywords
        
    Returns:
        dict: A dictionary with 'hashtags' containing a list of 8 hashtags
    """
    start_time = time.time()
    
    try:
        # Fetch popular hashtags using Serp API
        hashtags_result = fetch_popular_hashtags(
            general_persona_keywords=request.general_persona_keywords,
            content_persona_keywords=request.content_persona_keywords,
            network_persona_keywords=request.network_persona_keywords
        )
        # Concatenate and deduplicate
        hashtags = hashtags_result["persona_hashtags"] + hashtags_result["universal_hashtags"]
        seen = set()
        deduped_hashtags = []
        for tag in hashtags:
            tag_lower = tag.lower()
            if tag_lower not in seen:
                seen.add(tag_lower)
                deduped_hashtags.append(tag)
            if len(deduped_hashtags) == 8:
                break
        log_request_time("/popular-posts-hashtags", start_time)
        return JSONResponse(
            status_code=200,
            content={
                "success": True,
                "hashtags": deduped_hashtags,
                "message": "Popular hashtags fetched successfully"
            }
        )
    except ValueError as e:
        log_request_time("/popular-posts-hashtags", start_time)
        return JSONResponse(
            status_code=400,
            content={
                "success": False,
                "error": str(e),
                "message": "Configuration error"
            }
        )
    except Exception as e:
        log_request_time("/popular-posts-hashtags", start_time)
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "error": str(e),
                "message": "Failed to fetch popular hashtags"
            }
        )

@app.post("/todays-feed")
async def todays_feed(request: TodaysFeedRequest):
    """
    Categorize posts and provide recommendations for today's LinkedIn feed.
    
    This endpoint takes a list of posts and categorizes them based on content,
    then provides recommendations for reactions and comments for each post.
    
    Args:
        request (TodaysFeedRequest): The request containing posts and persona keywords
        
    Returns:
        dict: A dictionary with 'categories' containing categorized posts with recommendations
    """
    start_time = time.time()
    
    try:
        from app.utils.feed_categorizer import categorize_feed_posts
        
        # Categorize the posts and generate recommendations
        categorized_feed = await categorize_feed_posts(
            posts=request.posts,
            general_persona_keywords=request.general_persona_keywords,
            content_persona_keywords=request.content_persona_keywords,
            network_persona_keywords=request.network_persona_keywords
        )
        
        log_request_time("/todays-feed", start_time)
        
        return JSONResponse(
            status_code=200,
            content={
                "success": True,
                "categories": categorized_feed,
                "message": "Feed categorized successfully"
            }
        )
        
    except Exception as e:
        log_request_time("/todays-feed", start_time)
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "error": str(e),
                "message": "Failed to categorize feed"
            }
        )

@app.post("/suggest-comment-to-post")
async def suggest_comment_to_post(request: SuggestCommentRequest):
    """
    Suggest a comment for a LinkedIn post based on persona keywords and post content.
    Input:
        - general_persona_keywords: List[str]
        - content_persona_keywords: List[str] (optional)
        - network_persona_keywords: List[str] (optional)
        - content: str (the post content)
    Output:
        - comment: str (suggested comments)
    """
    try:
        from app.utils.prompt_templates_new import TEMPLATES
        from app.utils.model_initializer import model
        import json
        import random
        general_persona_keywords = request.general_persona_keywords
        content_persona_keywords = request.content_persona_keywords or []
        network_persona_keywords = request.network_persona_keywords or []
        post_content = request.content
        # Create a persona description for the prompt
        persona_description = f"Persona: {', '.join(general_persona_keywords)}. "
        if content_persona_keywords:
            persona_description += f"Content Interests: {', '.join(content_persona_keywords)}. "
        if network_persona_keywords:
            persona_description += f"Network Interests: {', '.join(network_persona_keywords)}. "
        # Enhance the prompt with explicit instruction
        prompt = (
            f"{persona_description}\n"
            "VOCABULARY REQUIREMENTS:\n"
            "- Use simple, clear words that everyone can understand\n"
            "- Avoid complex or fancy vocabulary - choose common, everyday words instead\n"
            "- Write at a level that is easy for all professionals to read and understand\n"
            "- Replace difficult words with simpler alternatives (e.g., 'use' instead of 'utilize', 'help' instead of 'facilitate')\n"
            "- Focus on clarity and understanding over sounding sophisticated\n"
            "- Make content accessible to professionals of all backgrounds and education levels\n\n"
            "You are a professional LinkedIn engagement strategist. Read the post content below and suggest a brief, authentic comment (MAX 2 sentences, MAX 40 words) that this persona would write in response. "
            "Make sure the comment is highly relevant to the post content and reflects the persona's expertise, interests, and professional style. "
            "Do not be generic; be specific and thoughtful. Do NOT exceed 2 sentences or 40 words.\n"
            f"--- Post Content ---\n{post_content}\n--- End Post Content ---\n"
            "Return ONLY a valid JSON object with this exact structure:\n"
            "{\n  \"suggested_comment\": \"Your suggested comment here\"\n}"
        )
        temperature = random.uniform(0.8, 1.2)
        response = model.generate_content(prompt, use_cache=False, temperature=temperature)
        response_text = response.text.strip()
        # Clean up the response text to extract JSON
        if "```json" in response_text:
            start = response_text.find("```json") + 7
            end = response_text.find("```", start)
            if end != -1:
                response_text = response_text[start:end].strip()
        elif "```" in response_text:
            start = response_text.find("```") + 3
            end = response_text.find("```", start)
            if end != -1:
                response_text = response_text[start:end].strip()
        try:
            result = json.loads(response_text)
            comment = result.get("suggested_comment", "Great insights!")
        except Exception:
            comment = "Thanks for sharing this valuable content!"
        return {"comment": comment}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error generating comment: {str(e)}")

@app.post("/score_profile")
async def score_profile_endpoint(profile: UserProfile):
    """
    Score a LinkedIn profile based on completeness and relevance.
    
    This endpoint evaluates a LinkedIn profile and provides:
    - Overall score out of 100
    - Individual scores for different sections (headline, summary, work experience, education, others)
    - Detailed evaluation and improvement suggestions
    
    Args:
        profile (UserProfile): The LinkedIn profile data to score
        
    Returns:
        dict: A dictionary containing the profile score and detailed analysis
    """
    start_time = time.time()
    try:
        score = score_profile(profile.model_dump())
        log_request_time("/score_profile", start_time)
        return score
    except Exception as e:
        log_request_time("/score_profile", start_time)
        raise HTTPException(status_code=500, detail=str(e))

# ============================================================================
# AGENTIC CHATBOT ENDPOINT
# ============================================================================

class ChatMessage(BaseModel):
    message: str = Field(description="User's message to the chatbot")
    session_id: str = Field(description="Unique session identifier for conversation continuity")

class ChatResponse(BaseModel):
    response: str = Field(description="Chatbot's response")
    session_id: str = Field(description="Session identifier")
    intent: Optional[str] = Field(description="Detected user intent")
    timestamp: str = Field(description="Response timestamp")

@app.post("/chat", response_model=ChatResponse)
async def chat_with_assistant(request: ChatMessage):
    """
    Agentic chatbot endpoint for conversational AI assistance with LinkedIn content creation.
    
    This intelligent assistant provides comprehensive LinkedIn content support through specialized agents:
    
    **Content Creation**: Generate original LinkedIn posts tailored to your professional brand
    **Content Modification**: Edit and improve existing posts with different tones/styles  
    **News Discovery**: Find trending industry news and content inspiration
    **Profile Analysis**: Analyze LinkedIn profiles and generate professional personas
    **Strategy Planning**: Develop content calendars and posting strategies
    **General Chat**: Handle conversations, guidance, and feature explanations
    
    The chatbot maintains conversation context, learns user preferences, and intelligently
    routes requests to appropriate specialist agents using LangChain and LangGraph.
    
    Args:
        request (ChatMessage): User's message and session ID for conversation continuity
        
    Returns:
        ChatResponse: Intelligent response with detected intent and metadata
        
    Example Usage:
        - "Write a post about my promotion to Tech Lead"
        - "Make this post more casual: [paste content]"  
        - "What's trending in AI this week?"
        - "Analyze my LinkedIn profile"
        - "Plan my content strategy for next month"
        - "Hello, what can you help me with?"
    """
    start_time = time.time()
    try:
        from app.chatbot.orchestrator import chatbot_orchestrator
        
        # Get response from the chatbot orchestrator
        response = await chatbot_orchestrator.chat(
            message=request.message,
            session_id=request.session_id
        )
        
        # Get the conversation state to extract intent
        from app.chatbot.state import state_manager
        conversation_state = state_manager.get_state(request.session_id)
        current_intent = conversation_state.current_intent
        
        log_request_time("/chat", start_time)
        
        return ChatResponse(
            response=response,
            session_id=request.session_id,
            intent=current_intent,
            timestamp=datetime.datetime.now().isoformat()
        )
        
    except Exception as e:
        log_request_time("/chat", start_time)
        # Provide a graceful fallback response
        fallback_response = f"""I apologize, but I encountered an issue processing your request: {str(e)}

However, I'm still here to help you with LinkedIn content creation! Here's what I can assist you with:

🎯 **Create Content** - Generate engaging LinkedIn posts
📰 **Find News** - Discover trending industry topics  
✏️ **Edit Posts** - Improve existing content
📊 **Plan Strategy** - Develop content calendars
👤 **Analyze Profiles** - Optimize your LinkedIn presence

What would you like to work on?"""
        
        return ChatResponse(
            response=fallback_response,
            session_id=request.session_id,
            intent="error_fallback",
            timestamp=datetime.datetime.now().isoformat()
        )

@app.post("/chat/profile/update")
async def update_user_profile(request: dict):
    """
    Update user profile information for personalized chatbot assistance.
    
    This endpoint allows updating user persona, preferences, and context information
    to provide more personalized and relevant assistance.
    
    Args:
        request (dict): Contains session_id and profile information
        
    Returns:
        dict: Success confirmation with updated profile summary
    """
    start_time = time.time()
    try:
        from app.chatbot.orchestrator import chatbot_orchestrator
        
        session_id = request.get("session_id")
        if not session_id:
            raise HTTPException(status_code=400, detail="session_id is required")
        
        # Prepare profile updates (remove session_id and None values)
        profile_updates = {k: v for k, v in request.items() 
                          if v is not None and k != "session_id"}
        
        success = await chatbot_orchestrator.update_user_profile(
            session_id=session_id,
            profile_updates=profile_updates
        )
        
        log_request_time("/chat/profile/update", start_time)
        
        if success:
            # Get updated profile for confirmation
            from app.chatbot.state import state_manager
            conversation_state = state_manager.get_state(session_id)
            profile_summary = conversation_state.get_user_context_summary()
            
            return {
                "message": "Profile updated successfully",
                "session_id": session_id,
                "updated_profile": profile_summary
            }
        else:
            raise HTTPException(status_code=500, detail="Failed to update profile")
            
    except Exception as e:
        log_request_time("/chat/profile/update", start_time)
        raise HTTPException(status_code=500, detail=f"Error updating profile: {str(e)}")