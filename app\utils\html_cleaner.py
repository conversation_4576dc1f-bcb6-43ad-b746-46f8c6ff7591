import re
import codecs
from bs4 import BeautifulSoup

def clean_html_content(content):
    """
    Clean HTML content to ensure only allowed tags are present.
    Allowed tags:
    - <p> for paragraphs
    - <br> for line breaks
    - <blockquote> for decorative quotes
    - <ul> for bullet lists
    - <ol> for numbered lists
    - <li> for list items
    - <b>, <strong> for bold
    - <i>, <em> for italic
    - <u> for underline
    - <s>, <del> for strikethrough
    - <code> for inline code (monospace)
    """
    # Decode bytes as UTF-8 if needed
    if isinstance(content, bytes):
        content = content.decode('utf-8', errors='replace')

    # Only decode as unicode_escape if it looks like escaped unicode (e.g., \\uXXXX)
    if re.search(r'\\u[0-9a-fA-F]{4}', content):
        try:
            content = codecs.decode(content, 'unicode_escape')
        except Exception:
            pass  # If decoding fails, leave content as is

    # Replace all newline variants with <br>
    content = re.sub(r'(\n\n|\n|\r\n|\r)', '<br>', content)

    # Convert **bold** to <strong> and *italic* to <em>
    content = re.sub(r'\*\*(.+?)\*\*', r'<strong>\1</strong>', content)
    content = re.sub(r'\*(.+?)\*', r'<em>\1</em>', content)

    # Parse the HTML content
    soup = BeautifulSoup(content, 'html.parser')
    
    # Define allowed tags and their attributes
    allowed_tags = {
        'p': ['class'],  # Allow class attribute for hashtags
        'br': [],
        'blockquote': [],
        'ul': [],
        'ol': [],
        'li': [],
        'b': [],
        'strong': [],
        'i': [],
        'em': [],
        'u': [],
        's': [],
        'del': [],
        'code': []
    }
    
    # Remove all tags that are not in the allowed list
    for tag in soup.find_all():
        if tag.name not in allowed_tags:
            tag.replace_with(tag.contents)
    
    # Remove disallowed attributes from allowed tags
    for tag in soup.find_all():
        if tag.name in allowed_tags:
            allowed_attrs = allowed_tags[tag.name]
            # Keep only allowed attributes
            tag.attrs = {attr: value for attr, value in tag.attrs.items() if attr in allowed_attrs}
    
    return str(soup) 