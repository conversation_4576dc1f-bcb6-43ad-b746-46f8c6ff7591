# post_management.py
from typing import List, Dict, Any, Optional
import random
from datetime import datetime, timedelta
import uuid
import re
from app.utils.post_creator import (
    generate_scheduled_post_content, 
    generate_post, 
    fetch_related_url,
    generate_search_query_from_content,
    analyze_post_for_media
)
import concurrent.futures

def clean_html_for_query(html_content: str) -> str:
    """Remove HTML tags and extra whitespace to create a clean query string."""
    # Remove HTML tags using regex
    clean_text = re.sub(r'<[^>]+>', '', html_content)
    # Replace newlines and multiple spaces with a single space
    clean_text = re.sub(r'\s+', ' ', clean_text).strip()
    return clean_text

def post_content_needs_url(content: str) -> bool:
    """Return True if the post content suggests a URL should be included."""
    url_cues = [
        'read more', 'source', 'case study', 'research', 'article', 'news',
        'find out more', 'learn more', 'full story', 'details here', 'see more',
        'reference', 'study', 'report', 'whitepaper', 'blog', 'external link',
        'original post', 'press release', 'announcement', 'further reading',
        'visit', 'explore', 'click here', 'link below', 'for more information'
    ]
    content_lower = content.lower()
    return any(cue in content_lower for cue in url_cues)

def should_post_have_url(post_content: str) -> bool:
    """Use LLM to determine if a post should include a URL."""
    from app.utils.post_creator import model
    prompt = (
        "Should this LinkedIn post include a relevant external URL? Respond only with true or false.\n"
        f"Post:\n{post_content}"
    )
    response = model.generate_content(prompt)
    answer = response.text.strip().lower()
    return answer.startswith('true')

def generate_post_schedule(persona_data, schedule_duration, start_date=None, end_date=None, template=None):
    # Generate schedule for a custom date range
    schedule = []
    import concurrent.futures
    import uuid
    import random
    import threading

    # Extract persona keywords and interests
    general_persona_keywords = persona_data.get('general_persona_keywords', [])
    content_interests = persona_data.get('content_interests', [])
    network_interests = persona_data.get('network_interests', [])
    content_persona_keywords = persona_data.get('content_persona_keywords', [])
    network_persona_keywords = persona_data.get('network_persona_keywords', [])
    categories = persona_data.get('categories', [])
    base_tone = persona_data.get('tone', "Professional")
    add_emojis = persona_data.get('add_emojis', True)  # Default to True for better engagement
    add_hashtags = persona_data.get('add_hashtags', True)

    # HOOK UNIQUENESS TRACKING - NEW FEATURE
    # Thread-safe tracking of used opening words across all scheduled posts
    used_opening_words = set()
    opening_words_lock = threading.Lock()

    # Parse start_date and end_date
    if start_date and end_date:
        start_dt = datetime.strptime(start_date, "%Y-%m-%d")
        end_dt = datetime.strptime(end_date, "%Y-%m-%d")
        num_days = (end_dt - start_dt).days + 1
    else:
        # fallback to 7 days if not provided
        start_dt = datetime.today()
        num_days = 7

    tones = ["Polite", "Witty", "Enthusiastic", "Friendly", "Informational", "Funny", "Professional", "Authoritative"]
    styles = ["Informative", "Thought-provoking", "Personal storytelling"]
    lengths = ["Short", "Medium", "Long"]

    # Determine category distribution based on the logic specified
    category_distribution = []
    if num_days < len(categories):
        selected_categories = categories[:num_days]
        category_distribution = selected_categories
    elif num_days == len(categories):
        category_distribution = categories
    else:
        base_count = num_days // len(categories)
        remainder = num_days % len(categories)
        category_distribution = categories * base_count
        if remainder > 0:
            random_categories = random.sample(categories, remainder)
            category_distribution.extend(random_categories)
        random.shuffle(category_distribution)

    def generate_single_day_post(day):
        # These must be re-imported in the thread context
        from app.utils.post_creator import generate_post_from_persona_keywords, analyze_post_for_media
        post_date = start_dt + timedelta(days=day)
        hour = random.randint(8, 18)  # 8 AM to 6 PM
        minute = random.choice([0, 15, 30, 45])
        post_time = f"{hour:02d}:{minute:02d}"
        post_datetime = datetime.combine(post_date.date(), datetime.strptime(post_time, "%H:%M").time())

        # For thread safety, use local variables for last_tone/style/length
        available_tones = tones[:]
        available_styles = styles[:]
        available_lengths = lengths[:]
        tone = base_tone if base_tone else random.choice(available_tones)
        style = random.choice(available_styles)
        length = random.choice(available_lengths)
        category = category_distribution[day] if day < len(category_distribution) else "General"
        custom_prompt = f"As a {', '.join(general_persona_keywords)}, write a {style.lower()} post about {category}."
        if content_persona_keywords and len(content_persona_keywords) > 0:
            custom_prompt += f" Focus on these topics: {', '.join(content_persona_keywords)}."
        if network_persona_keywords and len(network_persona_keywords) > 0:
            custom_prompt += f" Target audience: {', '.join(network_persona_keywords)}."
        
        # HOOK UNIQUENESS - Get current used opening words in thread-safe manner
        with opening_words_lock:
            current_used_words = used_opening_words.copy()

        # Add day index to the prompt to ensure variety across scheduled posts
        enhanced_prompt = f"{custom_prompt} [Schedule Day {day}, Variant for uniqueness]"

        post_content = generate_post_from_persona_keywords(
            general_persona_keywords,
            tone,
            style,
            enhanced_prompt,  # Use enhanced prompt with day info for variety
            content_interests=content_persona_keywords,
            network_interests=network_persona_keywords,
            add_emojis=add_emojis,  # Use the emoji setting from persona_data
            add_hashtags=add_hashtags,  # Use the hashtag setting from persona_data
            use_hook_generator=True,
            used_opening_words=current_used_words  # Pass the used words to avoid repetition
        )
        # Handle the new AI LinkedIn Expert System return format
        selected_framework = "Unknown"
        if isinstance(post_content, dict) and "posts" in post_content and len(post_content["posts"]) > 0:
            # For scheduling, use the first post (best framework match)
            first_post = post_content["posts"][0]
            post_content = first_post["content"]
            selected_framework = first_post.get("framework", "Unknown")
        elif isinstance(post_content, list) and len(post_content) > 0:
            # Handle list format
            first_post = post_content[0]
            if isinstance(first_post, dict):
                post_content = first_post.get("content", "")
                selected_framework = first_post.get("framework", "Unknown")
            else:
                post_content = str(first_post)
        elif isinstance(post_content, str):
            pass
        else:
            post_content = ""
        
        # HOOK UNIQUENESS - Extract and track the opening word from generated content
        if post_content:
            # Extract the first word from the post content (after HTML tags)
            import re
            # Remove HTML tags and get clean text
            clean_text = re.sub(r'<[^>]+>', '', post_content).strip()
            if clean_text:
                first_word = clean_text.split()[0].lower() if clean_text.split() else ""
                if first_word:
                    # Add the first word to the global set in a thread-safe manner
                    with opening_words_lock:
                        used_opening_words.add(first_word)
                        print(f"Day {day}: Added opening word '{first_word}' to used set. Total used: {len(used_opening_words)}")
        
        media_analysis = analyze_post_for_media(post_content)
        has_image = media_analysis.get("has_image", False)
        has_infographics = media_analysis.get("has_infographics", False)
        post_id = str(uuid.uuid4())
        return {
            "post_id": post_id,
            "scheduled_datetime": post_datetime.strftime("%Y-%m-%d %H:%M"),
            "style": style,
            "tone": tone,
            "length": length,
            "content": post_content,
            "category": category,
            "has_image": has_image,
            "has_infographics": has_infographics,
            "day_index": day
        }

    # Parallelize post generation for each day
    posts_data = []
    with concurrent.futures.ThreadPoolExecutor(max_workers=min(10, num_days)) as executor:
        future_to_day = {executor.submit(generate_single_day_post, day): day for day in range(num_days)}
        for future in concurrent.futures.as_completed(future_to_day):
            result = future.result()
            posts_data.append(result)
    # Restore original order
    posts_data.sort(key=lambda x: x["day_index"])
    for post in posts_data:
        del post["day_index"]

    # Second, determine which posts should have a URL (using LLM) and assign up to 3 URLs
    url_limit = 3 if num_days >= 7 else max(1, num_days // 2)
    url_count = 0
    assigned_urls = set()
    for post in posts_data:
        url = None
        if url_count < url_limit and should_post_have_url(post["content"]):
            clean_text = clean_html_for_query(post["content"])
            search_query = generate_search_query_from_content(clean_text)
            candidate_url = fetch_related_url(search_query)
            if candidate_url and candidate_url not in assigned_urls:
                url = candidate_url
                assigned_urls.add(url)
                url_count += 1
        post["url"] = url

    return posts_data

def get_post_insights(industry, target_audience):
    # Placeholder for post insights
    insights = {
        "optimal_time": "Morning",
        "target_audience": f"Professionals in {industry} Industry",
        "performance": "High engagement with informative posts"
    }
    return insights

def  generate_persona_posts(user_attributes):
    """Generate posts in all three styles for a user persona.

    Args:
        user_attributes: Dictionary containing user profile information

    Returns:
        Dictionary containing posts in all three styles
    """
    # Default values for tone, user_prompt, and length
    tone = "Professional"
    user_prompt = f"Write a post about {user_attributes.get('industry', 'technology')} industry trends"
    length = "medium"

    styles = ["Informative", "Thought-provoking", "Personal storytelling"]
    posts = {}

    for style in styles:
        post = generate_post(user_attributes, tone, style, user_prompt, length)
        posts[style.lower().replace('-', '_').replace(' ', '_')] = post

    return posts

def generate_persona_posts_from_keywords(general_persona_keywords, tone="Professional", user_prompt=None, length="medium", content_interests=None, network_interests=None, data=None):
    """Generate posts based on general persona keywords.

    Args:
        general_persona_keywords: List of keywords that represent the user's professional identity
        tone: The tone to use for the posts (default: "Professional")
        user_prompt: The user's prompt for the posts (default: None, will be generated based on keywords)
        length: The desired length of the posts (default: "medium")
        content_interests: Optional list of content interests
        network_interests: Optional list of network interests
        data: Optional dictionary containing post templates or topics. If provided, posts will be generated
             based on the content of this field along with the general persona keywords and interests.

    Returns:
        Dictionary containing generated posts. If data is provided, posts will be generated based on the data,
        general persona keywords, and interests. If data is not provided, posts will be generated in three
        standard styles based on general persona keywords and interests.
    """
    from app.utils.post_creator import generate_post_from_persona_keywords

    # If no user prompt is provided, generate one based on the keywords
    if not user_prompt:
        # Extract industry or profession from keywords if possible
        industry_keywords = [kw for kw in general_persona_keywords if any(term in kw.lower() for term in ["tech", "healthcare", "finance", "education", "marketing", "sales", "engineering", "design", "consulting", "legal", "hr", "recruitment"])]

        if industry_keywords:
            user_prompt = f"Write a post about trends in {industry_keywords[0]}"
        else:
            user_prompt = "Write a post about industry trends and professional insights"

    # Initialize posts dictionary
    posts = {}

    # If data is provided, generate posts based on the data, general persona keywords, and interests
    if data and isinstance(data, dict) and any(data):
        # For each key in data, generate a post
        for key, topic in data.items():
            # Create a custom prompt that incorporates the topic from data
            custom_prompt = f"As a {', '.join(general_persona_keywords)}, write about: {topic}"

            # If user_prompt is provided, incorporate it as well
            if user_prompt:
                custom_prompt = f"{custom_prompt}. Consider this additional context: {user_prompt}"

            # Add content interests if provided
            if content_interests and len(content_interests) > 0:
                custom_prompt = f"{custom_prompt}. Focus on these topics: {', '.join(content_interests)}"

            # Add network interests if provided
            if network_interests and len(network_interests) > 0:
                custom_prompt = f"{custom_prompt}. Target audience: {', '.join(network_interests)}"

            # Generate the post
            post = generate_post_from_persona_keywords(
                general_persona_keywords,
                tone,
                "Custom",  # Use a generic style since we're customizing based on the topic
                custom_prompt,
                length,
                content_interests,
                network_interests
            )
            # Handle the new three-variant return format
            if isinstance(post, dict) and "posts" in post and len(post["posts"]) > 0:
                # For persona posts, use the first persona-integrated post
                persona_posts = [p for p in post["posts"] if p.get("variant_type") == "persona-integrated"]
                if persona_posts:
                    post = persona_posts[0]["content"]
                else:
                    post = post["posts"][0]["content"]
            posts[key] = post
    else:
        # If no data is provided, generate posts in the three standard styles
        styles = ["Informative", "Thought-provoking", "Personal storytelling"]
        style_keys = [style.lower().replace('-', '_').replace(' ', '_') for style in styles]

        for style, style_key in zip(styles, style_keys):
            # Create a custom prompt that incorporates the general persona keywords and interests
            custom_prompt = f"As a {', '.join(general_persona_keywords)}"

            # If user_prompt is provided, incorporate it
            if user_prompt:
                custom_prompt = f"{custom_prompt}, {user_prompt}"
            else:
                custom_prompt = f"{custom_prompt}, write a {style.lower()} post about your professional field"

            # Add content interests if provided
            if content_interests and len(content_interests) > 0:
                custom_prompt = f"{custom_prompt}. Focus on these topics: {', '.join(content_interests)}"

            # Add network interests if provided
            if network_interests and len(network_interests) > 0:
                custom_prompt = f"{custom_prompt}. Target audience: {', '.join(network_interests)}"

            # Generate the post
            post = generate_post_from_persona_keywords(
                general_persona_keywords,
                tone,
                style,
                custom_prompt,
                length,
                content_interests,
                network_interests
            )
            # Handle the new three-variant return format
            if isinstance(post, dict) and "posts" in post and len(post["posts"]) > 0:
                # For persona posts, use the first persona-integrated post
                persona_posts = [p for p in post["posts"] if p.get("variant_type") == "persona-integrated"]
                if persona_posts:
                    post = persona_posts[0]["content"]
                else:
                    post = post["posts"][0]["content"]
            posts[style_key] = post

    return posts