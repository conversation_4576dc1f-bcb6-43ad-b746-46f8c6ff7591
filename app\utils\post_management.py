# post_management.py
from typing import List, Dict, Any, Optional
import random
from datetime import datetime, timedelta
import uuid
import re
import concurrent.futures
import threading
import logging

# --- IMPROVED: Import the async version of fetch_related_url ---
from app.utils.post_creator import (
    generate_post_from_persona_keywords, 
    fetch_related_url,
    generate_search_query_from_content,
    analyze_post_for_media,
    _parse_post_generation_response # <-- NEW: Import the centralized parser
)
from app.utils.model_initializer import model # Direct import for should_post_have_url

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')


def clean_html_for_query(html_content: str) -> str:
    """Remove HTML tags and extra whitespace to create a clean query string."""
    clean_text = re.sub(r'<[^>]+>', '', html_content)
    clean_text = re.sub(r'\s+', ' ', clean_text).strip()
    return clean_text

def should_post_have_url(post_content: str) -> bool:
    """Use LLM to determine if a post should include a URL."""
    prompt = (
        "Should this LinkedIn post include a relevant external URL? Respond only with true or false.\n"
        f"Post:\n{post_content}"
    )
    try:
        response = model.generate_content(prompt)
        answer = response.text.strip().lower()
        return answer.startswith('true')
    except Exception as e:
        logging.error(f"Error in should_post_have_url: {e}")
        return False

# --- NEW: Refactored helper function for calculating category distribution ---
def _calculate_category_distribution(num_days: int, categories: List[str]) -> List[str]:
    """Calculates the distribution of categories over the number of days."""
    if not categories:
        return ["General"] * num_days
        
    if num_days <= len(categories):
        distribution = categories[:num_days]
    else:
        base_count = num_days // len(categories)
        remainder = num_days % len(categories)
        distribution = categories * base_count
        if remainder > 0:
            distribution.extend(random.sample(categories, remainder))
    
    random.shuffle(distribution)
    return distribution

# --- NEW: Refactored and optimized post generation task for parallel execution ---
def _generate_and_process_post_for_day(day_index: int, start_dt: datetime, category: str, persona_data: Dict, used_opening_words: set, opening_words_lock: threading.Lock, url_budget: threading.Semaphore) -> Dict:
    """
    Generates a single post, analyzes it, and fetches a URL if needed.
    This function is designed to be run in a separate thread.
    """
    post_date = start_dt + timedelta(days=day_index)
    post_datetime_str = (post_date.replace(
        hour=random.randint(8, 18), 
        minute=random.choice([0, 15, 30, 45]))
    ).strftime("%Y-%m-%d %H:%M")

    # Build prompt for this day
    custom_prompt = f"As a {', '.join(persona_data.get('general_persona_keywords', []))}, write a {persona_data.get('style', 'thought-provoking').lower()} post about {category}. [Schedule Day {day_index}, Variant for uniqueness]"
    if persona_data.get('content_persona_keywords'):
        custom_prompt += f" Focus on these topics: {', '.join(persona_data['content_persona_keywords'])}."
    if persona_data.get('network_persona_keywords'):
        custom_prompt += f" Target audience: {', '.join(persona_data['network_persona_keywords'])}."

    with opening_words_lock:
        current_used_words = used_opening_words.copy()

    # Generate post content
    raw_post_response = generate_post_from_persona_keywords(
        persona_data.get('general_persona_keywords', []),
        persona_data.get('tone', "Professional"),
        persona_data.get('style', "Informative"),
        custom_prompt,
        content_interests=persona_data.get('content_persona_keywords'),
        network_interests=persona_data.get('network_persona_keywords'),
        add_emojis=persona_data.get('add_emojis', True),
        add_hashtags=persona_data.get('add_hashtags', True),
        use_hook_generator=True,
        used_opening_words=current_used_words
    )

    # --- IMPROVED: Use centralized parser ---
    parsed_post = _parse_post_generation_response(raw_post_response)
    if not parsed_post:
        logging.warning(f"Failed to generate or parse post for day {day_index}.")
        # Return a minimal structure to avoid breaking the schedule
        return {
            "post_id": str(uuid.uuid4()),
            "scheduled_datetime": post_datetime_str,
            "content": "Content generation failed for this slot. Please regenerate.",
            "category": category,
            "day_index": day_index,
            "has_image": False, "has_infographics": False, "url": None
        }

    post_content = parsed_post["content"]

    # Track unique opening words
    if post_content:
        clean_text = clean_html_for_query(post_content)
        first_word = clean_text.split()[0].lower() if clean_text else ""
        if first_word:
            with opening_words_lock:
                if first_word not in used_opening_words:
                    used_opening_words.add(first_word)

    # Media analysis and URL fetching
    media_analysis = analyze_post_for_media(post_content)
    post_url = None

    # --- IMPROVED: Integrated URL logic within the thread ---
    if url_budget.acquire(blocking=False): # Non-blocking attempt to acquire semaphore
        if should_post_have_url(post_content):
            try:
                import asyncio
                clean_text = clean_html_for_query(post_content)
                search_query = generate_search_query_from_content(clean_text)
                # Run the async function in the current thread's event loop
                post_url = asyncio.run(fetch_related_url(search_query))
            except Exception as e:
                logging.error(f"Error fetching URL for day {day_index}: {e}")
                url_budget.release() # Release budget if URL fetch fails

    return {
        "post_id": str(uuid.uuid4()),
        "scheduled_datetime": post_datetime_str,
        "style": persona_data.get('style', "Informative"),
        "tone": persona_data.get('tone', "Professional"),
        "length": "Medium", # Length can be dynamic; using a placeholder
        "content": post_content,
        "category": category,
        "has_image": media_analysis.get("has_image", False),
        "has_infographics": media_analysis.get("has_infographics", False),
        "url": post_url,
        "day_index": day_index
    }


# --- REFACTORED: Main scheduling function is now cleaner and more efficient ---
def generate_post_schedule(persona_data, schedule_duration, start_date=None, end_date=None, template=None):
    """
    Generates a post schedule using parallel processing with integrated URL fetching.
    """
    if start_date and end_date:
        start_dt = datetime.strptime(start_date, "%Y-%m-%d")
        end_dt = datetime.strptime(end_date, "%Y-%m-%d")
        num_days = (end_dt - start_dt).days + 1
    else:
        start_dt = datetime.today()
        num_days = 7

    # Setup for parallel execution
    used_opening_words = set()
    opening_words_lock = threading.Lock()
    
    # --- IMPROVED: Use a semaphore to limit the number of URLs ---
    url_limit = 3 if num_days >= 7 else max(1, num_days // 2)
    url_budget = threading.Semaphore(url_limit)
    
    categories = persona_data.get('categories', [])
    category_distribution = _calculate_category_distribution(num_days, categories)
    
    posts_data = []
    persona_data['style'] = random.choice(["Informative", "Thought-provoking", "Personal storytelling"])

    with concurrent.futures.ThreadPoolExecutor(max_workers=min(10, num_days)) as executor:
        future_to_day = {
            executor.submit(
                _generate_and_process_post_for_day,
                day,
                start_dt,
                category_distribution[day],
                persona_data,
                used_opening_words,
                opening_words_lock,
                url_budget
            ): day for day in range(num_days)
        }
        
        for future in concurrent.futures.as_completed(future_to_day):
            try:
                result = future.result()
                posts_data.append(result)
            except Exception as e:
                logging.error(f"A post generation task failed: {e}")

    # Final processing and cleanup
    posts_data.sort(key=lambda x: x["day_index"])
    for post in posts_data:
        del post["day_index"]

    return posts_data

def get_post_insights(industry, target_audience):
    # Placeholder for post insights
    insights = {
        "optimal_time": "Morning",
        "target_audience": f"Professionals in {industry} Industry",
        "performance": "High engagement with informative posts"
    }
    return insights

def  generate_persona_posts(user_attributes):
    """Generate posts in all three styles for a user persona.

    Args:
        user_attributes: Dictionary containing user profile information

    Returns:
        Dictionary containing posts in all three styles
    """
    # Default values for tone, user_prompt, and length
    tone = "Professional"
    user_prompt = f"Write a post about {user_attributes.get('industry', 'technology')} industry trends"
    length = "medium"

    styles = ["Informative", "Thought-provoking", "Personal storytelling"]
    posts = {}

    from app.utils.post_creator import generate_post_from_persona_keywords
    for style in styles:
        post = generate_post_from_persona_keywords(
            user_attributes.get('general_persona_keywords', []),
            tone,
            style,
            user_prompt,
            length,
            user_attributes.get('content_persona_keywords'),
            user_attributes.get('network_persona_keywords')
        )
        posts[style.lower().replace('-', '_').replace(' ', '_')] = post

    return posts

def generate_persona_posts_from_keywords(general_persona_keywords, tone="Professional", user_prompt=None, length="medium", content_interests=None, network_interests=None, data=None):
    """Generate posts based on general persona keywords.

    Args:
        general_persona_keywords: List of keywords that represent the user's professional identity
        tone: The tone to use for the posts (default: "Professional")
        user_prompt: The user's prompt for the posts (default: None, will be generated based on keywords)
        length: The desired length of the posts (default: "medium")
        content_interests: Optional list of content interests
        network_interests: Optional list of network interests
        data: Optional dictionary containing post templates or topics. If provided, posts will be generated
             based on the content of this field along with the general persona keywords and interests.

    Returns:
        Dictionary containing generated posts. If data is provided, posts will be generated based on the data,
        general persona keywords, and interests. If data is not provided, posts will be generated in three
        standard styles based on general persona keywords and interests.
    """
    from app.utils.post_creator import generate_post_from_persona_keywords

    # If no user prompt is provided, generate one based on the keywords
    if not user_prompt:
        # Extract industry or profession from keywords if possible
        industry_keywords = [kw for kw in general_persona_keywords if any(term in kw.lower() for term in ["tech", "healthcare", "finance", "education", "marketing", "sales", "engineering", "design", "consulting", "legal", "hr", "recruitment"])]

        if industry_keywords:
            user_prompt = f"Write a post about trends in {industry_keywords[0]}"
        else:
            user_prompt = "Write a post about industry trends and professional insights"

    # Initialize posts dictionary
    posts = {}

    # If data is provided, generate posts based on the data, general persona keywords, and interests
    if data and isinstance(data, dict) and any(data):
        # For each key in data, generate a post
        for key, topic in data.items():
            # Create a custom prompt that incorporates the topic from data
            custom_prompt = f"As a {', '.join(general_persona_keywords)}, write about: {topic}"

            # If user_prompt is provided, incorporate it as well
            if user_prompt:
                custom_prompt = f"{custom_prompt}. Consider this additional context: {user_prompt}"

            # Add content interests if provided
            if content_interests and len(content_interests) > 0:
                custom_prompt = f"{custom_prompt}. Focus on these topics: {', '.join(content_interests)}"

            # Add network interests if provided
            if network_interests and len(network_interests) > 0:
                custom_prompt = f"{custom_prompt}. Target audience: {', '.join(network_interests)}"

            # Generate the post
            post = generate_post_from_persona_keywords(
                general_persona_keywords,
                tone,
                "Custom",  # Use a generic style since we're customizing based on the topic
                custom_prompt,
                length,
                content_interests,
                network_interests
            )
            # Handle the new three-variant return format
            if isinstance(post, dict) and "posts" in post and len(post["posts"]) > 0:
                # For persona posts, use the first persona-integrated post
                persona_posts = [p for p in post["posts"] if p.get("variant_type") == "persona-integrated"]
                if persona_posts:
                    post = persona_posts[0]["content"]
                else:
                    post = post["posts"][0]["content"]
            posts[key] = post
    else:
        # If no data is provided, generate posts in the three standard styles
        styles = ["Informative", "Thought-provoking", "Personal storytelling"]
        style_keys = [style.lower().replace('-', '_').replace(' ', '_') for style in styles]

        for style, style_key in zip(styles, style_keys):
            # Create a custom prompt that incorporates the general persona keywords and interests
            custom_prompt = f"As a {', '.join(general_persona_keywords)}"

            # If user_prompt is provided, incorporate it
            if user_prompt:
                custom_prompt = f"{custom_prompt}, {user_prompt}"
            else:
                custom_prompt = f"{custom_prompt}, write a {style.lower()} post about your professional field"

            # Add content interests if provided
            if content_interests and len(content_interests) > 0:
                custom_prompt = f"{custom_prompt}. Focus on these topics: {', '.join(content_interests)}"

            # Add network interests if provided
            if network_interests and len(network_interests) > 0:
                custom_prompt = f"{custom_prompt}. Target audience: {', '.join(network_interests)}"

            # Generate the post
            post = generate_post_from_persona_keywords(
                general_persona_keywords,
                tone,
                style,
                custom_prompt,
                length,
                content_interests,
                network_interests
            )
            # Handle the new three-variant return format
            if isinstance(post, dict) and "posts" in post and len(post["posts"]) > 0:
                # For persona posts, use the first persona-integrated post
                persona_posts = [p for p in post["posts"] if p.get("variant_type") == "persona-integrated"]
                if persona_posts:
                    post = persona_posts[0]["content"]
                else:
                    post = post["posts"][0]["content"]
            posts[style_key] = post

    return posts