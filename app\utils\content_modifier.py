# content_modifier.py

from app.utils.model_initializer import model
import hashlib
import time
import re
import emoji
import random # <--- Make sure this import is at the top of your file

# ... (the rest of your file up to the get_suggested_emojis function remains the same) ...

def _get_modification_cache_key(original_content: str, tone: str, style: str, length: str) -> str:
    # ... (This function is unchanged)
    content_hash = hashlib.md5(original_content.encode()).hexdigest()
    params_str = f"rewrite_{tone}_{style}_{length}"
    params_hash = hashlib.md5(params_str.encode()).hexdigest()
    return f"{content_hash}_{params_hash}"

def modify_content(original_content: str, tone: str, style: str, length: str, disable_cache: bool = False) -> str:
    # ... (This function is unchanged)
    cache_key = _get_modification_cache_key(original_content, tone, style, length)
    
    if not disable_cache and cache_key in _modification_cache:
        return _modification_cache[cache_key]
    
    clean_original_content = re.sub(r"<p class='hashtags'>.*?</p>", "", original_content, flags=re.DOTALL).strip()
    
    length_guidance = ""
    if length == "short":
        length_guidance = "For this SHORT post, the Hook must be very direct. The Body should be 1-2 concise paragraphs. The CTA must be a simple, clear question or directive."
    elif length == "medium":
        length_guidance = "For this MEDIUM post, the Hook can be an intriguing question or a bold statement. The Body should be well-developed with paragraphs and/or a bulleted list. The CTA can be more thought-provoking."
    elif length == "long":
        length_guidance = "For this LONG post, the Hook can be a short personal anecdote or a surprising statistic. The Body must be comprehensive, with multiple sections or detailed points. The CTA should encourage a detailed response or discussion."

    prompt_template = f"""
You are an expert LinkedIn content strategist and copywriter. Your mission is to transform the following raw text into a complete, professional, and engaging LinkedIn post from start to finish. You must rewrite the entire text.

**--- CORE REQUIREMENTS ---**
1.  **Rewrite Everything:** You MUST rewrite the entire original text to fit the new requirements.
2.  **Follow the Structure:** Every post you generate MUST contain the three essential parts: The Hook, The Body, and The Call to Action.
3.  **Adhere to Parameters:** The final post must match the specified Tone, Style, and Length.

**--- POST STRUCTURE (HOOK-BODY-CTA) ---**
1.  **The Hook (First Sentence):** Start with a compelling, attention-grabbing opening sentence.
2.  **The Body (Main Content):** Develop the core message here. Explain the main points clearly.
3.  **The Call to Action (CTA) (Last Sentence):** End with a clear and engaging question or directive.

**--- PARAMETERS FOR THIS POST ---**
-   **Tone:** {tone}
-   **Style:** {style}
-   **Length:** {length.capitalize()} ({_calculate_dynamic_length_range(clean_original_content, length)['min_chars']}-{_calculate_dynamic_length_range(clean_original_content, length)['max_chars']} chars)
-   **Length-Specific Guidance:** {length_guidance}

**--- RICH TEXT FORMATTING RULES ---**
-   **Bulleted Lists (<ul><li>):** Actively look for opportunities to convert a series of points into a bulleted list.
-   **Numbered Lists (<ol><li>):** If the content describes steps or a sequence, use a numbered list.
-   **Emphasis (<strong>):** Use sparingly to highlight the most critical keywords.

**--- ORIGINAL RAW TEXT TO TRANSFORM ---**
{clean_original_content}

**--- YOUR TASK ---**
Now, generate the complete, rewritten LinkedIn post in clean HTML format.

**Final LinkedIn Post (HTML):**
"""

    response = model.generate_content(prompt_template, use_cache=(not disable_cache), temperature=0.8)
    modified_content = response.text.strip()
    modified_content = re.sub(r"^```html\s*|\s*```$", "", modified_content).strip()

    if not disable_cache:
        _modification_cache[cache_key] = modified_content
    
    return modified_content


# --- THIS IS THE ONLY FUNCTION YOU NEED TO REPLACE ---
def get_suggested_emojis(text: str, length: str = 'medium') -> list:
    """
    Analyzes text and returns a DIFFERENT list of contextually relevant emojis on every hit.
    """
    if not text:
        return []

    # Determine the number of emojis to request
    length = length.lower()
    if length == 'long':
        emoji_count = random.randint(5, 8)
        count_instruction = f"Suggest exactly {emoji_count} emojis for this long, detailed post."
    elif length == 'short':
        emoji_count = random.randint(2, 4)
        count_instruction = f"Suggest {emoji_count} emojis for this short, concise post."
    else: # Medium
        emoji_count = random.randint(3, 5)
        count_instruction = f"Suggest {emoji_count} emojis for this medium-length post."

    # --- NEW: Add a randomness seed to the prompt ---
    # This unique seed forces the AI to generate a new response every time.
    random_seed = f"{int(time.time() * 1000)}_{random.randint(1000, 9999)}"
    
    clean_text = re.sub(r'<[^>]+>', '', text).strip()
    
    prompt = f"""
Analyze the professional content below for its core themes and tone.
{count_instruction} The emojis must be highly relevant and professional.

**CRITICAL REQUIREMENT:** You MUST provide a different and varied set of emojis on every request. Use the randomization seed below to ensure your output is unique.

**Content for Analysis:**
"{clean_text}"

**Randomization Seed (for ensuring unique output):** {random_seed}

**Instructions:**
- Return ONLY the emoji characters, separated by a single space. Do not include any other text.

**Suggested Emojis:**
"""
    # --- NEW: Disable cache and slightly increase temperature for more variety ---
    response = model.generate_content(prompt, temperature=0.85, use_cache=False)
    emoji_string = response.text.strip()
    
    return [char for char in emoji_string if char in emoji.EMOJI_DATA]

# ... (the rest of your file, like insert_emojis_into_html, remains the same) ...

def insert_emojis_into_html(html_text: str, emojis: list) -> str:
    # ... (This function is unchanged)
    if not emojis or not html_text: return html_text
    
    paragraphs = re.findall(r'(<p>.*?</p>)', html_text, re.DOTALL)
    if not paragraphs:
        return f"{html_text} {' '.join(emojis)}"

    for i in range(min(len(emojis), len(paragraphs))):
        para_to_modify = paragraphs[i]
        modified_para = para_to_modify.replace("</p>", f" {emojis[i]}</p>", 1)
        html_text = html_text.replace(para_to_modify, modified_para, 1)
        
    if len(emojis) > len(paragraphs) and paragraphs:
        remaining_emojis = ' '.join(emojis[len(paragraphs):])
        last_para = paragraphs[-1]
        modified_last_para = last_para.replace("</p>", f" {remaining_emojis}</p>", 1)
        temp_html_text = html_text.replace(last_para, modified_last_para)
        html_text = temp_html_text

    return html_text

def _calculate_dynamic_length_range(original_content: str, length_param: str) -> dict:
    # ... (This function is unchanged)
    length_param = length_param.lower()
    if length_param == "short": min_chars, max_chars = 400, 800
    elif length_param == "medium": min_chars, max_chars = 800, 1500
    elif length_param == "long": min_chars, max_chars = 1500, 2500
    else: min_chars, max_chars = 400, 1500
    return {"min_chars": min_chars, "max_chars": max_chars}

def clear_modification_cache():
    # ... (This function is unchanged)
    global _modification_cache
    _modification_cache.clear()