import json
from app.utils.model_initializer import model
from datetime import datetime

EXPERIENCE_CATEGORIES = {
    "Entry Level": (0, 1),
    "Junior": (1, 3),
    "Mid-Level": (3, 7),
    "Senior": (7, 15),
    "Expert": (15, 100)
}

CEO_TITLES = ["CEO", "CTO", "<PERSON><PERSON>", "CO<PERSON>", "C<PERSON>", "<PERSON><PERSON>"]

def calculate_duration(start_date, end_date):
    # Extract year from date string (handles YYYY-MM-DD format)
    try:
        if start_date and start_date != '0':
            # Handle YYYY-MM-DD format
            if '-' in start_date:
                start_year = int(start_date.split('-')[0])
            else:
                # Fallback for other formats
                start_year = int(start_date.split()[-1])
        else:
            start_year = 0
    except (ValueError, AttributeError):
        start_year = 0

    if end_date and end_date.lower() == 'present':
        end_year = datetime.now().year
    else:
        try:
            if end_date and end_date != '0':
                # Handle YYYY-MM-DD format
                if '-' in end_date:
                    end_year = int(end_date.split('-')[0])
                else:
                    # Fallback for other formats
                    end_year = int(end_date.split()[-1])
            else:
                end_year = 0
        except (ValueError, AttributeError):
            end_year = 0

    return max(0, end_year - start_year)

def categorize_experience(work_experience):
    total_years = sum(calculate_duration(job.get("startDate", "0"), job.get("endDate", "0")) for job in work_experience)
    for category, (min_exp, max_exp) in EXPERIENCE_CATEGORIES.items():
        if min_exp <= total_years < max_exp:
            return category

    for job in work_experience:
        if job.get("position") in CEO_TITLES:
            return "Upper Management"

    return "Unknown"

def check_missing_attributes(profile_data):
    missing_attributes_suggestions = []

    attributes_to_check = {
        "name": "Name",
        "headline": "Headline",
        "summary": "Summary",
        "work_experience": "Work Experience",
        "current_role": "Current Role",
        "organizations": "Organization",
        "industry": "Industry",
        "city": "City",
        "country": "Country",
        "skills": "Skills",
        "certifications": "Certifications",
        "languages": "Languages",
        "education": "Education (Degrees, Institutions, Years of Study)"
    }

    for attribute_key, attribute_name in attributes_to_check.items():
        if not profile_data.get(attribute_key):
            missing_attributes_suggestions.append(f"- Add a '{attribute_name}' to make your profile more complete.")

    missing_attributes_text = "\n".join(missing_attributes_suggestions) if missing_attributes_suggestions else "Profile attributes are reasonably complete."
    return missing_attributes_text, missing_attributes_suggestions

def score_profile(profile_data):
    experience_category = categorize_experience(profile_data.get("work_experience", []))
    missing_attributes_text, missing_attributes_suggestions = check_missing_attributes(profile_data)

    score_prompt = f"""
    Evaluate the LinkedIn profile based on the following attributes:
    Name: {profile_data.get('name', 'N/A')}
    Headline: {profile_data.get('headline', 'N/A')}
    Summary: {profile_data.get('summary', 'N/A')}
    Work Experience: {profile_data.get('work_experience', 'N/A')}
    Current Role: {profile_data.get('current_role', 'N/A')}
    Organization: {profile_data.get('organizations', 'N/A')}
    Industry: {profile_data.get('industry', 'N/A')}
    City: {profile_data.get('city', 'N/A')}
    Country: {profile_data.get('country', 'N/A')}
    Skills: {profile_data.get('skills', [])}
    Certifications: {profile_data.get('certifications', [])}
    Languages: {profile_data.get('languages', [])}
    Education: {profile_data.get('education', [])}
    Experience Level: {experience_category}

    **Profile Completeness Check:**
    {missing_attributes_text}

    Evaluate the provided LinkedIn profile and assign a score out of 100 based on completeness and relevance. Provide individual scores for the following attributes:

    Headline
    Summary
    Work Experience
    Education
    Others (all remaining attributes)
    Additionally, suggest improvements tailored to the individual's experience level.

    Instructions:
    Return the response in JSON format with the following keys:

    {{
    "overall_score": 0-100,
    "headline_score": 0-100,
    "summary_score": 0-100,
    "work_experience_score": 0-100,
    "education_score": 0-100,
    "others_score": 0-100,
    "evaluation": "Brief analysis of the profile",
    "suggestions": {{
        "headline": "Improvement suggestions for headline",
        "summary": "Improvement suggestions for summary",
        "work_experience": "Suggestions for work experience section",
        "education": "Suggestions for education section",
        "skills": "Recommendations for skills section",
        "certifications": "Suggestions for adding or improving certifications",
        "languages": "Recommendations related to language proficiency",
        "general": "Overall suggestions to enhance profile completeness and relevance"
    }}
    }}
    """

    try:
        response = model.generate_content(score_prompt)
        response_text = response.text.strip()
        
        # Clean up the response text to extract JSON
        if "```json" in response_text:
            start = response_text.find("```json") + 7
            end = response_text.find("```", start)
            if end != -1:
                response_text = response_text[start:end].strip()
        elif "```" in response_text:
            start = response_text.find("```") + 3
            end = response_text.find("```", start)
            if end != -1:
                response_text = response_text[start:end].strip()
        
        json_response = json.loads(response_text)
        return json_response
    except Exception as e:
        return {"error": f"Error scoring profile: {e}"}


