import re
import concurrent.futures
import hashlib
import logging
import random
import time
import os
import requests
from functools import lru_cache
from typing import List, Dict, Any, Optional
from app.utils.model_initializer import model
from app.utils.prompt_templates_new import TEMPLATES
from app.utils.hashtag_generator import generate_hashtags
from app.utils.html_cleaner import clean_html_content

# Cache for expensive operations
_post_cache = {}
_media_analysis_cache = {}

def clean_cut(text, min_len, max_len, reserve_for_hashtags=0):
    """Trims text to ensure it's between min_len and max_len characters, ending at a complete sentence.

    Args:
        text: The text to trim
        min_len: Minimum length required
        max_len: Maximum length allowed
        reserve_for_hashtags: Space to reserve for hashtags (default: 0)

    Returns:
        Text trimmed to be between min_len and max_len, ending at a complete sentence
    """
    # If text is already within limits, return it as is
    if min_len <= len(text) <= max_len - reserve_for_hashtags:
        return text

    # If text is too long, trim it
    if len(text) > max_len - reserve_for_hashtags:
        # Split into sentences
        all_sentences = re.split(r'(?<=[.!?]) +', text)
        result = ''

        # Add sentences until we reach the max length
        for sentence in all_sentences:
            if len(result) + len(sentence) + 1 <= max_len - reserve_for_hashtags:
                result += sentence + ' '
            else:
                # If we haven't reached minimum length yet but adding this sentence would exceed max length,
                # we need to include part of this sentence to reach minimum length
                if len(result) < min_len and len(result) < max_len - reserve_for_hashtags:
                    # Calculate how many more characters we can add
                    remaining_space = max_len - reserve_for_hashtags - len(result)
                    # Add as much of the sentence as possible without exceeding max_len
                    result += sentence[:remaining_space].strip() + '...'
                break

        return result.strip()

    # If text is too short, return it as is (the caller should handle this case)
    return text

@lru_cache(maxsize=1000)
def _get_length_constraints(length):
    """Cache length constraints to avoid repeated calculations."""
    length = length.strip().lower()
    # LinkedIn-appropriate length constraints
    # LinkedIn has a 3000 character limit, but optimal engagement is with shorter posts
    length_ranges = {
        "short": (200, 500),      # Quick insights, tips, or questions
        "medium": (500, 1000),    # Standard LinkedIn post length
        "long": (1000, 1500)      # Detailed posts, but still LinkedIn-appropriate
    }
    return length_ranges.get(length, (500, 1000))

def get_framework_guidance_by_length(length: str, framework_name: str) -> str:
    """Get specific framework guidance based on post length."""
    length = length.strip().lower()
    
    if framework_name == "PAS (Problem-Agitate-Solve)":
        if length == "short":
            return "Focus on: 1 clear problem statement + 1-2 agitation sentences + 1-2 solution sentences. Be concise and impactful."
        elif length == "medium":
            return "Structure: 1-2 problem sentences + 2-3 agitation sentences + 2-3 solution sentences. Balance all components."
        else:  # long
            return "Expand on: 2-3 problem sentences + 3-4 agitation sentences + 3-5 solution sentences. Include examples and details."
    
    elif framework_name == "AIDA (Attention-Interest-Desire-Action)":
        if length == "short":
            return "Compress: 1 attention hook + 1-2 interest sentences + 1-2 desire sentences + 1 action CTA. Every word counts."
        elif length == "medium":
            return "Balance: 1-2 attention sentences + 2-3 interest sentences + 2-3 desire sentences + 1-2 action sentences."
        else:  # long
            return "Elaborate: 2-3 attention sentences + 3-4 interest sentences + 3-4 desire sentences + 2-3 action sentences."
    
    elif framework_name == "Story Arc (Beginning-Middle-End)":
        if length == "short":
            return "Condense: 1-2 setup sentences + 1-2 conflict sentences + 1-2 resolution sentences. Focus on one key moment."
        elif length == "medium":
            return "Develop: 2-3 beginning sentences + 3-4 middle sentences + 2-3 end sentences. Include emotional journey."
        else:  # long
            return "Expand: 3-4 beginning sentences + 4-6 middle sentences + 3-4 end sentences. Rich details and full narrative."
    
    return "Follow the framework structure within the specified character limits."

def generate_post(user_attributes, tone, style, user_prompt, length):
    min_len, max_len = _get_length_constraints(length)
    length_description = f"{min_len} to {max_len} characters (with complete sentences)"

    # Build user profile
    work_experience_str = ""
    if user_attributes.get("work_experience"):
        work_experience_str = "\nWork Experience:\n"
        for exp in user_attributes["work_experience"]:
            work_experience_str += (
                f"- {exp.get('position', 'Position')}, {exp.get('company', 'Company')}, "
                f"{exp.get('location', 'Location')}, {exp.get('startDate', 'Start Date')} - {exp.get('endDate', 'End Date')}, "
                f"Industry: {exp.get('industry', 'Industry')}, Description: {exp.get('job_description', 'No Description')}\n"
            )

    education_str = ""
    if user_attributes.get("education"):
        education_str = "\nEducation:\n"
        for edu in user_attributes["education"]:
            education_str += f"- {edu.get('degree', 'Degree')}, {edu.get('school_name', 'School')}, {edu.get('years_of_study', 'Years')}\n"

    # Add interests if available
    interests_str = ""
    if user_attributes.get("interests"):
        interests_str = f"Interests: {', '.join(user_attributes.get('interests', ['N/A']))}\n"

    user_profile = (
        f"Name: {user_attributes.get('name', 'N/A')}\n"
        f"Headline: {user_attributes.get('headline', 'N/A')}\n"
        f"Summary: {user_attributes.get('summary', 'N/A')}\n"
        f"Current Role: {user_attributes.get('current_role', 'N/A')}\n"
        f"Organizations: {user_attributes.get('organizations', 'N/A')}\n"
        f"Industry: {user_attributes.get('industry', 'N/A')}\n"
        f"Location: {user_attributes.get('city', 'N/A')}, {user_attributes.get('country', 'N/A')}\n"
        f"Skills: {', '.join(user_attributes.get('skills', ['N/A']))}\n"
        f"Certifications: {', '.join(user_attributes.get('certifications', ['N/A']))}\n"
        f"Languages: {', '.join(user_attributes.get('languages', ['N/A']))}\n"
        f"{interests_str}"
        f"{work_experience_str}"
        f"{education_str}"
    )

    # Update the prompt template to request HTML formatting
    prompt_template = TEMPLATES["post_creation"]
    prompt = prompt_template.format(
        user_profile=user_profile,
        tone=tone,
        style=style,
        user_prompt=user_prompt,
        length_description=length_description
    ) + """
IMPORTANT: Format the post in HTML with proper tags:
- Use <p> for paragraphs
- Use <ul> and <li> for lists
- Use <h2> for section headers
- Use <strong> for emphasis on key points
- Use <em> for subtle emphasis
- Do not include any hashtags or emojis in the post.
- Keep the HTML clean and semantic.
"""

    # Generate post and ensure it meets length and content criteria
    retries = 3
    for _ in range(retries):
        response = model.generate_content(prompt)
        content = response.text.strip()

        # Check if content meets length requirements
        content_length = len(content)

        # Reserve space for hashtags (typically 80-120 characters for LinkedIn)
        hashtags_reserve = 120

        # If content is too short or too long, try to fix it
        if content_length < min_len or content_length > max_len - hashtags_reserve:
            # If too long, trim it to fit within max_len while preserving complete sentences
            if content_length > max_len - hashtags_reserve:
                content = clean_cut(content, min_len, max_len - hashtags_reserve)

            # If content is now within the desired range, break the loop
            if min_len <= len(content) <= max_len - hashtags_reserve:
                break
        else:
            # Content is already within the desired range
            break

    # Check if content is just hashtags (placeholder response)
    if content.startswith('#') and all(word.startswith('#') for word in content.split()):
        # If it's just hashtags, replace with a proper placeholder post
        content = """<p>Artificial Intelligence is revolutionizing industries across the globe, and as a technology professional, I'm excited to share some key trends I've been observing.</p>

<p>First, generative AI has moved beyond simple text generation to become a powerful tool for content creation, code development, and problem-solving. Companies are now integrating these capabilities into their workflows, significantly boosting productivity and enabling new forms of creativity.</p>

<p>Second, the democratization of AI tools is accelerating. What once required specialized knowledge and expensive infrastructure is now accessible through user-friendly platforms and APIs. This shift is enabling professionals from various backgrounds to leverage AI in their work.</p>

<p>Third, responsible AI development has become a central focus. Organizations are increasingly aware of the ethical implications of AI systems and are implementing frameworks to ensure fairness, transparency, and accountability.</p>

<p>As we navigate this rapidly evolving landscape, continuous learning and adaptation are essential. I'm curious to hear your thoughts on these trends and how they're impacting your work.</p>

<p>What AI applications are you most excited about in your industry?</p>"""

    # Generate hashtags and add them to the post
    interests = user_attributes.get('interests', [])
    hashtags = generate_hashtags_for_post(content, interests)
    
    # Add hashtags as a separate paragraph
    content = f"{content}\n<p class='hashtags'>{hashtags}</p>"
    
    # Final validation - ensure the post doesn't exceed LinkedIn's 3000 character limit
    final_length = len(content)
    if final_length > 3000:
        # Remove hashtags section temporarily for trimming
        hashtags_section = content.split("<p class='hashtags'>")[1].strip()
        content = content.split("<p class='hashtags'>")[0].strip()
        
        # Trim the content to fit within 3000 chars minus hashtags space
        hashtags_length = len(hashtags_section)
        available_space = 3000 - hashtags_length - 10  # 10 chars for spacing
        content = clean_cut(content, min_len, available_space)
        
        # Re-add hashtags
        content = f"{content}\n<p class='hashtags'>{hashtags_section}</p>"
    
    return content

def generate_hashtags_for_post(post_content, interests=None):
    """Generate hashtags for a LinkedIn post.

    Args:
        post_content: The content of the post
        interests: Optional list of user interests to consider for hashtag generation

    Returns:
        String of hashtags separated by spaces
    """
    return generate_hashtags(post_content, interests)

def ensure_cta_at_end(content):
    """Ensure the post ends with a call-to-action (CTA) using AI-generated content."""
    # Check if the content already ends with a question or CTA-like phrase
    cta_indicators = [
        "?",  # Questions
        "What are your thoughts",
        "What do you think",
        "How do you",
        "What's your experience",
        "Let's discuss",
        "Share your thoughts",
        "I'd love to hear",
        "What's your take",
        "How do you approach",
        "What challenges have you faced",
        "What strategies work for you",
        "What's your perspective",
        "How do you handle",
        "What would you do",
        "What's your opinion",
        "How do you see",
        "What's your approach",
        "What do you think about",
        "How do you feel about"
    ]
    
    # Check if content already ends with a CTA
    content_lower = content.lower()
    
    # Check for question marks (most common CTA indicator)
    has_question = "?" in content
    
    # Check for CTA phrases
    has_cta_phrase = any(indicator.lower() in content_lower for indicator in cta_indicators)
    
    # Check if the last paragraph contains engagement language
    paragraphs = content.split("<p>")
    if paragraphs:
        last_paragraph = paragraphs[-1].lower()
        has_engagement = any(word in last_paragraph for word in ["thoughts", "experience", "discuss", "share", "opinion", "perspective", "approach", "challenge"])
    
    has_cta = has_question or has_cta_phrase or has_engagement
    
    # Always ensure there's a CTA - be more aggressive about it
    if not has_cta or not content.strip().endswith("?"):
        # Generate a contextual CTA using AI
        cta = generate_contextual_cta(content)
        
        # Add the CTA before hashtags if they exist
        if "<p class='hashtags'>" in content:
            parts = content.split("<p class='hashtags'>")
            content = f"{parts[0].strip()}\n\n<p>{cta}</p>\n\n<p class='hashtags'>{parts[1]}"
        else:
            content = f"{content.strip()}\n\n<p>{cta}</p>"
    
    return content

def generate_contextual_cta(content):
    """Generate a contextually appropriate call-to-action using AI."""
    try:
        # Create a prompt for generating a contextual CTA
        prompt = f"""
        CRITICAL TASK: Generate a call-to-action (CTA) for this LinkedIn post.
        
        The post is missing a CTA and MUST end with one. Create a single, engaging CTA that:
        1. Is directly relevant to the post's topic and content
        2. Encourages reader engagement and discussion
        3. Is natural, conversational, and professional
        4. Ends with a question mark or invitation
        5. Is 1-2 sentences maximum
        
        Post content:
        {content}
        
        IMPORTANT: Return ONLY the CTA text, no explanations or additional text. Make it engaging and specific to the content.
        """
        
        # Generate CTA using the AI model
        response = model.generate_content(prompt, use_cache=False)
        cta = response.text.strip()
        
        # Clean up the response
        if cta.startswith('"') and cta.endswith('"'):
            cta = cta[1:-1]
        
        # Remove any introductory text
        if "CTA:" in cta:
            cta = cta.split("CTA:")[-1].strip()
        if "Call-to-action:" in cta:
            cta = cta.split("Call-to-action:")[-1].strip()
        
        return cta
        
    except Exception as e:
        print(f"Error generating contextual CTA: {str(e)}")
        # Fallback to a simple, generic CTA
        return "What are your thoughts on this?"

def add_intelligent_emojis_to_post(post_content, persona_keywords=None, content_interests=None):
    """
    Add contextually relevant emojis to a post using intelligent content analysis.

    Args:
        post_content (str): The original post content
        persona_keywords (list): User's persona keywords for context
        content_interests (list): User's content interests for context

    Returns:
        str: Post content with intelligently selected emojis added
    """
    from app.utils.model_initializer import model

    # Create context information for better emoji selection
    context_info = ""
    if persona_keywords:
        context_info += f"User's professional background: {', '.join(persona_keywords[:5])}\n"
    if content_interests:
        context_info += f"Content interests: {', '.join(content_interests[:5])}\n"

    # Create an intelligent emoji selection prompt
    emoji_prompt = f"""
You are an expert in LinkedIn content optimization and emoji selection. Analyze the following LinkedIn post and add 2-4 contextually relevant, professional emojis that enhance the message.

{context_info}

CONTENT ANALYSIS REQUIREMENTS:
1. Analyze the post's main themes, topics, and emotional tone
2. Identify key concepts that would benefit from emoji emphasis
3. Consider the professional context and industry relevance
4. Determine optimal placement points for maximum impact

EMOJI SELECTION CRITERIA:
- Choose emojis that directly relate to the specific content themes
- Select emojis appropriate for LinkedIn's professional audience
- Avoid generic or overused emojis unless they perfectly match the content
- Consider industry-specific emojis when relevant (tech: 💻🔧⚡, business: 📊💼🎯, etc.)
- Match the emotional tone of the content (inspirational, informative, celebratory, etc.)

PLACEMENT GUIDELINES:
- Place emojis at natural pause points or emphasis moments
- Use emojis to break up longer text sections
- Position emojis where they enhance meaning, not distract
- Integrate emojis smoothly into the content flow
- Vary placement (beginning, middle, end) based on content structure

QUANTITY GUIDELINES:
- Use 2-4 emojis total based on content length and complexity
- Shorter posts (under 500 chars): 2-3 emojis
- Longer posts (500+ chars): 3-4 emojis
- Adjust quantity based on natural emphasis points

Original post:
{post_content}

Return the enhanced post with intelligently selected emojis integrated naturally. Maintain the exact HTML structure and formatting. Do not add explanations or comments - return only the enhanced post content.
"""

    try:
        # Generate the enhanced post with intelligent emoji selection
        response = model.generate_content(emoji_prompt)
        enhanced_content = response.text.strip()

        # Clean up any markdown formatting
        if "```html" in enhanced_content:
            enhanced_content = enhanced_content.split("```html")[1].split("```", 1)[0].strip()
        elif "```" in enhanced_content:
            enhanced_content = enhanced_content.split("```", 1)[1].strip()

        # Remove any remaining code block markers
        enhanced_content = enhanced_content.replace("```html", "").replace("```", "").strip()

        return enhanced_content

    except Exception as e:
        print(f"Error adding intelligent emojis to post: {e}")
        return post_content  # Return original content if emoji addition fails

def add_emojis_to_post(post_content, template):
    """
    Legacy function for backward compatibility.
    Now redirects to the intelligent emoji system.
    """
    return add_intelligent_emojis_to_post(post_content)

@lru_cache(maxsize=500)
def generate_search_query_from_content(content: str) -> str:
    """Uses the AI model to generate a concise search query from post content."""
    try:
        # Create a prompt to generate a focused search query
        prompt = f"""
        Based on the following LinkedIn post, what is the best 3-5 word search query to find a highly relevant article?
        
        Post:
        "{content}"
        
        Respond with ONLY the search query, and nothing else.
        """
        
        response = model.generate_content(prompt, use_cache=True)
        search_query = response.text.strip().replace('"', '')
        
        # As a fallback, if the query is too long, truncate it
        if len(search_query.split()) > 7:
            return " ".join(content.split()[:10]) # Fallback to first 10 words
            
        return search_query

    except Exception as e:
        print(f"Error generating search query: {str(e)}")
        # Fallback to using the first 10 words of the content as the query
        return " ".join(content.split()[:10])

def fetch_related_url(query: str) -> Optional[str]:
    """Fetch a real, accessible recent news URL using the SerpApi news engine and a focused query."""
    import re
    # Get the API key from an environment variable
    serpapi_key = os.environ.get("SERPAPI_KEY")

    if not serpapi_key:
        print("SERPAPI_KEY environment variable not set. Returning placeholder URL.")
        # Clean the query to create a slug
        query_clean = re.sub(r'[^a-z0-9\s-]', '', query.lower()).strip()
        query_clean = re.sub(r'[\s-]+', '-', query_clean)[:50]
        return f"https://example.com/article/{query_clean}"

    try:
        # Use a focused search query for better relevance
        focused_query = generate_search_query_from_content(query)
        params = {
            'api_key': serpapi_key,
            'q': focused_query,
            'engine': 'google_news',  # Use the news engine
            'num': 1,  # Get just the top result
            'tbs': 'qdr:w'  # News from the last week
        }

        response = requests.get('https://serpapi.com/search', params=params)
        response.raise_for_status()  # Raise an exception for bad status codes

        data = response.json()

        if 'news_results' in data and data['news_results']:
            # Return the first (most recent and relevant) news result's link
            return data['news_results'][0]['link']
        elif 'organic_results' in data and data['organic_results']:
            # Fallback: Return the first organic result if no news found
            return data['organic_results'][0]['link']

        return None

    except requests.exceptions.RequestException as e:
        print(f"Error calling SERP API: {str(e)}")
        return None
    except Exception as e:
        print(f"Error processing SERP API response: {str(e)}")
        return None

def analyze_user_intent_and_perspective(user_prompt: str) -> Dict[str, Any]:
    """
    Intelligent intent and perspective analysis using AI to understand user's actual intent.

    Returns:
        Dictionary containing detailed intent analysis including perspective, role, and content type
    """
    from app.utils.model_initializer import model

    if not user_prompt or not user_prompt.strip():
        return {
            "intent_type": "general",
            "perspective": "third_person",
            "user_role": "observer",
            "target_audience": "general_professionals",
            "content_style": "informative",
            "confidence": 0.5
        }

    analysis_prompt = f"""
    Analyze the following user prompt to determine their intent and perspective for creating a LinkedIn post.

    User Prompt: "{user_prompt}"

    Provide a detailed analysis in the following JSON format:

    {{
        "intent_type": "personal|advice|educational|promotional|commentary",
        "perspective": "first_person|second_person|third_person",
        "user_role": "job_seeker|advisor|recruiter|expert|student|entrepreneur|employee|manager|other",
        "target_audience": "peers|job_seekers|employers|general_professionals|industry_experts|students|other",
        "content_style": "personal_story|how_to_guide|industry_insight|announcement|question|reflection|other",
        "confidence": 0.0-1.0,
        "key_indicators": ["list", "of", "key", "words", "or", "phrases", "that", "led", "to", "this", "analysis"],
        "reasoning": "Brief explanation of why this analysis was chosen"
    }}

    Analysis Guidelines:
    1. INTENT_TYPE:
       - "personal": User sharing their own experience, situation, or journey (e.g., "I am looking for a job", "I just got promoted")
       - "advice": User providing guidance or tips to others (e.g., "How to find a remote job", "Tips for interviews")
       - "educational": User teaching concepts or sharing knowledge (e.g., "Understanding AI in healthcare")
       - "promotional": User marketing their services, products, or achievements (e.g., "Check out my new course")
       - "commentary": User commenting on industry trends, news, or general topics

    2. PERSPECTIVE:
       - "first_person": About the user themselves (I, me, my, we, us, our)
       - "second_person": Directed at the audience (you, your, addressing readers directly)
       - "third_person": General commentary (they, them, it, general statements)

    3. USER_ROLE: What role is the user taking in this context?

    4. TARGET_AUDIENCE: Who is the intended audience for this post?

    5. CONTENT_STYLE: What type of content structure would best serve this intent?

    6. CONFIDENCE: How confident are you in this analysis (0.0 = very uncertain, 1.0 = very certain)

    7. KEY_INDICATORS: Specific words, phrases, or grammatical structures that led to this analysis

    8. REASONING: Brief explanation of the analysis

    CRITICAL: Pay special attention to pronouns and sentence structure. "I am looking for a job" should be classified as personal/first_person, not advice.

    Return ONLY the JSON object, no additional text.
    """

    try:
        response = model.generate_content(analysis_prompt, use_cache=False)
        analysis_text = response.text.strip()

        # Clean up the response to extract JSON
        if "```json" in analysis_text:
            analysis_text = analysis_text.split("```json")[1].split("```")[0].strip()
        elif "```" in analysis_text:
            analysis_text = analysis_text.split("```")[1].strip()

        # Parse the JSON response
        import json
        analysis_result = json.loads(analysis_text)

        # Validate and set defaults for missing fields
        required_fields = {
            "intent_type": "general",
            "perspective": "third_person",
            "user_role": "professional",
            "target_audience": "general_professionals",
            "content_style": "informative",
            "confidence": 0.7,
            "key_indicators": [],
            "reasoning": "AI analysis completed"
        }

        for field, default_value in required_fields.items():
            if field not in analysis_result:
                analysis_result[field] = default_value

        return analysis_result

    except Exception as e:
        print(f"Error in intent analysis: {str(e)}")
        # Return safe defaults
        return {
            "intent_type": "general",
            "perspective": "third_person",
            "user_role": "professional",
            "target_audience": "general_professionals",
            "content_style": "informative",
            "confidence": 0.5,
            "key_indicators": [],
            "reasoning": "Fallback analysis due to processing error"
        }

def classify_prompt_and_select_frameworks(user_prompt: str, general_persona_keywords: List[str]) -> Dict[str, Any]:
    """
    AI LinkedIn Expert System - Step 1-3: Classify prompt and select appropriate frameworks
    Now uses intelligent intent analysis instead of hardcoded keywords.

    Returns:
        Dictionary containing prompt classification and selected frameworks with reasons
    """
    # Step 1: Use AI-powered intent analysis
    intent_analysis = analyze_user_intent_and_perspective(user_prompt)

    # Map the new intent analysis to the existing framework selection logic
    intent_type = intent_analysis.get("intent_type", "general")
    perspective = intent_analysis.get("perspective", "third_person")
    user_role = intent_analysis.get("user_role", "professional")

    # Determine if prompt is persona-based based on perspective and intent
    is_persona_based = (
        perspective == "first_person" or
        intent_type == "personal" or
        len(general_persona_keywords) > 3
    )

    # Map intent types to the existing intent categories for framework selection
    intent_mapping = {
        "personal": "Authority",  # Personal stories show expertise through experience
        "advice": "Informative",  # Advice posts are informative
        "educational": "Informative",  # Educational content is informative
        "promotional": "Awareness",  # Promotional content creates awareness
        "commentary": "Engagement"  # Commentary encourages discussion
    }

    detected_intent = intent_mapping.get(intent_type, "Informative")

    # Step 2: Prompt Weightage
    prompt_length = len(user_prompt.split()) if user_prompt else 0
    is_detailed_prompt = prompt_length > 20

    # Step 3: Framework Selection - Choose 3 distinct frameworks
    available_frameworks = [
        {
            "name": "AIDA",
            "full_name": "Attention-Interest-Desire-Action",
            "best_for": ["Awareness", "Engagement", "Branding"],
            "typical_length": "medium",
            "description": "Get attention, build interest, create want, and ask for action"
        },
        {
            "name": "PAS",
            "full_name": "Problem-Agitation-Solution",
            "best_for": ["Authority", "Informative"],
            "typical_length": "medium",
            "description": "Point out a problem, show why it hurts, and give the solution"
        },
        {
            "name": "Story Arc",
            "full_name": "Beginning-Middle-End Narrative",
            "best_for": ["Authority", "Branding", "Engagement"],
            "typical_length": "long",
            "description": "A complete story with setup, challenge, and lesson learned"
        },
        {
            "name": "Before-After-Bridge",
            "full_name": "Before-After-Bridge Framework",
            "best_for": ["Informative", "Authority"],
            "typical_length": "medium",
            "description": "Show current situation, ideal situation, and how to get there"
        },
        {
            "name": "Listicle",
            "full_name": "List-based Content Framework",
            "best_for": ["Informative", "Engagement"],
            "typical_length": "long",
            "description": "Clear list format with useful tips"
        },
        {
            "name": "Question-led",
            "full_name": "Question-led Engagement Framework",
            "best_for": ["Engagement", "Authority"],
            "typical_length": "short",
            "description": "Start with interesting question and give insights"
        },
        {
            "name": "Data-Driven Persuasion",
            "full_name": "Data-Driven Persuasion Framework",
            "best_for": ["Authority", "Informative"],
            "typical_length": "medium",
            "description": "Start with surprising statistics, explain why they matter, and give one clear action step"
        },
        {
            "name": "Credible Spotlight",
            "full_name": "Credible Spotlight Framework",
            "best_for": ["Authority", "Branding"],
            "typical_length": "medium",
            "description": "Highlight real people or organizations while connecting to personal values and bigger missions"
        },
        {
            "name": "Counterintuitive Leadership Truth",
            "full_name": "Counterintuitive Leadership Truth Framework",
            "best_for": ["Authority", "Engagement"],
            "typical_length": "short",
            "description": "Challenge common beliefs with surprising insights and give better ways to do things"
        }
    ]

    # Select 3 frameworks with improved randomization to prevent AIDA bias
    import random
    import time

    # Create a more random seed to ensure better distribution
    current_time = int(time.time() * 1000)
    prompt_hash = hash(user_prompt) if user_prompt else 0
    random_seed = (current_time + prompt_hash + len(general_persona_keywords)) % 10000
    random.seed(random_seed)

    selected_frameworks = []

    # Step 1: Shuffle available frameworks to prevent order bias
    shuffled_frameworks = available_frameworks.copy()
    random.shuffle(shuffled_frameworks)

    # Step 2: Create weighted selection based on intent match
    # Instead of just filtering, create a weighted selection system
    framework_scores = []
    for framework in shuffled_frameworks:
        base_score = 1.0  # Base score for all frameworks

        # Add bonus for intent match
        if detected_intent in framework["best_for"]:
            base_score += 2.0

        # Add variety bonus to prevent AIDA over-selection
        if framework["name"] == "AIDA":
            base_score *= 0.8  # Slightly reduce AIDA's selection probability

        # Add bonus for less common frameworks
        if framework["name"] in ["Before-After-Bridge", "Question-led", "Data-Driven Persuasion", "Credible Spotlight", "Counterintuitive Leadership Truth"]:
            base_score *= 1.2  # Boost less commonly selected frameworks

        framework_scores.append((framework, base_score))

    # Step 3: Select primary framework using weighted random selection
    total_weight = sum(score for _, score in framework_scores)
    random_value = random.uniform(0, total_weight)
    cumulative_weight = 0

    for framework, score in framework_scores:
        cumulative_weight += score
        if random_value <= cumulative_weight:
            selected_frameworks.append(framework)
            break

    # Step 4: Select secondary framework with different characteristics
    remaining_frameworks = [f for f in shuffled_frameworks if f not in selected_frameworks]

    if remaining_frameworks:
        # Prefer different length and different intent focus
        primary_length = selected_frameworks[0]["typical_length"]
        primary_intents = set(selected_frameworks[0]["best_for"])

        # Score remaining frameworks for variety
        secondary_scores = []
        for framework in remaining_frameworks:
            variety_score = 1.0

            # Bonus for different length
            if framework["typical_length"] != primary_length:
                variety_score += 1.5

            # Bonus for different intent focus
            framework_intents = set(framework["best_for"])
            if not framework_intents.intersection(primary_intents):
                variety_score += 1.0

            secondary_scores.append((framework, variety_score))

        # Select secondary framework
        total_secondary_weight = sum(score for _, score in secondary_scores)
        random_secondary = random.uniform(0, total_secondary_weight)
        cumulative_secondary = 0

        for framework, score in secondary_scores:
            cumulative_secondary += score
            if random_secondary <= cumulative_secondary:
                selected_frameworks.append(framework)
                break

    # Step 5: Select third framework for maximum variety
    remaining_frameworks = [f for f in shuffled_frameworks if f not in selected_frameworks]
    if remaining_frameworks:
        # Simply pick randomly from remaining to ensure variety
        selected_frameworks.append(random.choice(remaining_frameworks))

    # Ensure we have exactly 3 frameworks
    while len(selected_frameworks) < 3:
        available_for_fallback = [f for f in shuffled_frameworks if f not in selected_frameworks]
        if available_for_fallback:
            selected_frameworks.append(random.choice(available_for_fallback))
        else:
            # Last resort - duplicate a framework but this should rarely happen
            selected_frameworks.append(random.choice(shuffled_frameworks))

    return {
        "is_persona_based": is_persona_based,
        "intent": detected_intent,
        "is_detailed_prompt": is_detailed_prompt,
        "selected_frameworks": selected_frameworks,
        "intent_analysis": intent_analysis  # Include the detailed analysis
    }

def generate_framework_reason(framework: Dict, intent: str, is_persona_based: bool, user_prompt: str) -> str:
    """Generate explanation for why this framework was chosen"""
    base_reasons = {
        "AIDA": f"AIDA framework chosen because the content needs to get attention and drive action, which fits the Attention-Interest-Desire-Action flow",
        "PAS": f"PAS framework picked to address the specific problem mentioned and give a clear solution",
        "Story Arc": f"Story Arc framework used to create an interesting story that connects personally with readers while sharing the key message",
        "Before-After-Bridge": f"Before-After-Bridge framework chosen to clearly show change and give useful steps",
        "Listicle": f"Listicle format picked to present information in an easy-to-read, useful way",
        "Question-led": f"Question-led approach chosen to get people engaged and encourage them to join the discussion",
        "Data-Driven Persuasion": f"Data-Driven Persuasion framework chosen to use compelling statistics to persuade and give clear action steps",
        "Credible Spotlight": f"Credible Spotlight framework picked to highlight real achievements while connecting to personal values and bigger missions",
        "Counterintuitive Leadership Truth": f"Counterintuitive Leadership Truth framework chosen to challenge common thinking and provide better leadership approaches"
    }

    reason = base_reasons.get(framework["name"], f"{framework['name']} framework chosen because it works well for sharing the intended message")

    # Add context based on intent and persona
    if intent == "Authority":
        reason += " to show expertise and build trust"
    elif intent == "Engagement":
        reason += " to get more people to interact and discuss"
    elif intent == "Branding":
        reason += " to strengthen brand identity and values"

    if is_persona_based:
        reason += ", including personal work context in a natural way"

    return reason

def create_framework_guidance(framework: Dict, tone_style: Dict, is_persona_based: bool) -> str:
    """Create comprehensive framework guidance for post generation"""

    guidance = f"""
FRAMEWORK: {framework['name']} - {framework['full_name']}
DESCRIPTION: {framework['description']}
TONE: {tone_style['tone']} - {tone_style['style']}
APPROACH: {tone_style['approach']}

FRAMEWORK-SPECIFIC REQUIREMENTS:
"""

    # Add framework-specific guidance
    if framework["name"] == "AIDA":
        guidance += """
- ATTENTION: Start with a strong hook that makes people stop and read
- INTEREST: Build curiosity with useful insights or questions
- DESIRE: Create want or need by showing benefits or making an emotional connection
- ACTION: End with a clear request for people to engage
"""
    elif framework["name"] == "PAS":
        guidance += """
- PROBLEM: Point out a specific problem people can relate to
- AGITATION: Show the pain points and what happens if nothing changes
- SOLUTION: Give a clear, useful solution or way to fix it
"""
    elif framework["name"] == "Story Arc":
        guidance += """
- BEGINNING: Set up the situation or context
- MIDDLE: Show the challenge, conflict, or journey
- END: Share the outcome, lesson, or insight learned
- Include personal details and emotional connection
"""
    elif framework["name"] == "Before-After-Bridge":
        guidance += """
- Start by describing the current problem or difficult situation
- Then paint a picture of what the ideal situation would look like
- Finally, provide steps or ways to get from the problem to the solution
- Use natural transitions between these sections without explicit labels
"""
    elif framework["name"] == "Listicle":
        guidance += """
- Clear numbered or bulleted list
- Each point should be useful and valuable
- Include short explanations for each point
- Strong start and finish
"""
    elif framework["name"] == "Question-led":
        guidance += """
- Start with a question that makes people think
- Give insights that answer the question
- Ask readers to share their thoughts
- Keep it short but powerful
"""
    
    elif framework["name"] == "Data-Driven Persuasion":
        guidance += """
- HOOK: Start with one surprising statistic, percentage, or trend
- INTERPRETATION: Explain why this data matters and what it means
- ACTION: Give one clear, doable step the audience can take
- PROOF: Share a case study or example that shows it works
- CTA: Ask people to try it and share their results
"""
    elif framework["name"] == "Credible Spotlight":
        guidance += """
- CREDIBILITY: Feature a real person, event, or milestone
- PERSONAL CONNECTION: Explain why this matters to you personally
- DETAILS: Share 2-3 specific things about their achievements
- BIG PICTURE: Connect to a larger cause or mission
- ENGAGEMENT: Ask for reflection or support from readers
"""
    elif framework["name"] == "Counterintuitive Leadership Truth":
        guidance += """
- CONTRARIAN THESIS: Challenge a common belief with surprising insight
- WHY IT FAILS: Explain why the old belief doesn't work
- BETTER ALTERNATIVE: Present a new approach that works better
- PRACTICAL STEP: Give one thing they can try right away
- MEMORABLE CLOSING: End with a quotable line or reflection question
"""

    guidance += f"""
LENGTH: Let content flow naturally based on what the framework needs (max 3000 characters)
PERSONAL TOUCH: {"Include personal context in a natural way" if is_persona_based else "Keep it general so it applies to many people"}
"""

    return guidance


def select_post_with_url(posts: List[str], query: str) -> Dict[str, Any]:
    """Select the best post to pair with a URL and fetch the URL.
    Args:
        posts: List of generated posts
        query: The original query/prompt
    Returns:
        Dictionary with selected post index and URL
    """
    try:
        # Analyze each post for media suitability
        posts_with_media = []
        for post in posts:
            media_analysis = analyze_post_for_media(post)
            posts_with_media.append({
                "content": post,
                "has_image": media_analysis["has_image"],
                "has_infographics": media_analysis["has_infographics"]
            })

        # Use select_best_post to choose the most relevant post
        selected_index = select_best_post(posts_with_media)
        selected_post_content = posts[selected_index]

        # Use the content of the selected post as the SerpAPI query
        url = fetch_related_url(selected_post_content)

        return {
            "selected_index": selected_index,
            "url": url
        }
    except Exception as e:
        print(f"Error selecting post with URL: {str(e)}")
        return {
            "selected_index": 0,
            "url": None
        }

@lru_cache(maxsize=500)
def analyze_post_for_media(post_content: str) -> dict:
    """Analyze a post to determine if it would benefit from images or infographics.
    
    Args:
        post_content: The content of the post
        
    Returns:
        Dictionary with has_image and has_infographics flags
    """
    try:
        # Simple heuristic-based analysis
        content_lower = post_content.lower()
        
        # Check for data/statistics indicators (more specific)
        data_indicators = [
            'percent', '%', 'statistics', 'data', 'comparison', 'chart', 'graph',
            'numbers', 'figures', 'analysis', 'survey', 'study', 'research',
            'increase', 'decrease', 'growth', 'decline', 'trend', 'survey results',
            'market share', 'revenue', 'profit', 'cost', 'efficiency', 'performance metrics'
        ]
        
        # Check for visual content indicators (more specific)
        visual_indicators = [
            'image', 'photo', 'picture', 'visual', 'design', 'layout',
            'color', 'brand', 'logo', 'icon', 'illustration', 'screenshot',
            'before and after', 'comparison image', 'product image', 'team photo'
        ]
        
        # Check for process/step indicators (more specific)
        process_indicators = [
            'step', 'process', 'workflow', 'pipeline', 'framework',
            'methodology', 'approach', 'strategy', 'plan', 'roadmap',
            'timeline', 'phases', 'stages', 'cycle', 'lifecycle'
        ]
        
        # Count occurrences to determine strength of indicators
        data_count = sum(1 for indicator in data_indicators if indicator in content_lower)
        visual_count = sum(1 for indicator in visual_indicators if indicator in content_lower)
        process_count = sum(1 for indicator in process_indicators if indicator in content_lower)
        
        # Determine media recommendations with more nuanced logic
        has_strong_data = data_count >= 3  # Increased threshold - need more data indicators
        has_strong_visual = visual_count >= 2  # Need multiple visual indicators
        has_strong_process = process_count >= 3  # Increased threshold - need more process indicators
        
        # Content length consideration
        content_length = len(post_content)
        is_long_content = content_length > 1000
        
        # Determine media type based on content analysis
        has_infographics = False
        has_image = False
        
        # Priority 1: Strong data indicators suggest infographics
        if has_strong_data:
            has_infographics = True
        # Priority 2: Strong visual indicators suggest images
        elif has_strong_visual:
            has_image = True
        # Priority 3: Strong process indicators suggest infographics
        elif has_strong_process:
            has_infographics = True
        # Priority 4: Long content without specific indicators might benefit from images
        elif is_long_content and not (has_strong_data or has_strong_visual or has_strong_process):
            has_image = True
        # Priority 5: Only consider infographics if we have multiple data indicators (at least 2)
        elif data_count >= 2 and not has_strong_visual and not has_strong_process:
            has_infographics = True
        # Priority 6: If we have some visual indicators but not strong enough, still consider images
        elif visual_count >= 1 and not has_strong_data and not has_strong_process:
            has_image = True
        
        # Ensure mutual exclusivity
        if has_infographics and has_image:
            # If both are true, prioritize based on content type
            if has_strong_data or has_strong_process:
                has_image = False
                has_infographics = True
            elif has_strong_visual:
                has_image = True
                has_infographics = False
            else:
                # Default to infographics for data-heavy content
                has_image = False
                has_infographics = True
        
        return {
            "has_image": has_image,
            "has_infographics": has_infographics
        }
    except Exception as e:
        print(f"Error analyzing post for media: {str(e)}")
        return {
            "has_image": False,
            "has_infographics": False
        }

def select_best_post(posts_with_media):
    """Select the best post from a list of posts with media analysis.
    
    Args:
        posts_with_media: List of dictionaries with post content and media analysis
        
    Returns:
        Index of the best post
    """
    try:
        # Simple selection logic - prefer posts with infographics, then images
        best_index = 0
        best_score = 0
        
        for i, post_data in enumerate(posts_with_media):
            score = 0
            if post_data.get("has_infographics"):
                score += 3
            if post_data.get("has_image"):
                score += 2
            if len(post_data.get("content", "")) > 1000:
                score += 1
                
            if score > best_score:
                best_score = score
                best_index = i
                
        return best_index
    except Exception as e:
        print(f"Error selecting best post: {str(e)}")
        return 0

def validate_framework_completeness(content: str, framework_name: str) -> bool:
    """Validate that a post contains all required framework components."""
    content_lower = content.lower()
    
    if framework_name == "PAS (Problem-Agitate-Solve)":
        # Check for solution/solve indicators
        solution_indicators = ['solution', 'solve', 'fix', 'answer', 'approach', 'way to', 'how to', 'method', 'strategy']
        has_solution = any(indicator in content_lower for indicator in solution_indicators)
        
        # Check for problem indicators
        problem_indicators = ['problem', 'issue', 'challenge', 'struggle', 'difficulty', 'pain', 'frustration']
        has_problem = any(indicator in content_lower for indicator in problem_indicators)
        
        return has_problem and has_solution
        
    elif framework_name == "AIDA (Attention-Interest-Desire-Action)":
        # Check for action/CTA indicators
        action_indicators = ['?', 'what', 'how', 'share', 'tell', 'comment', 'thoughts', 'experience', 'join', 'connect']
        has_action = any(indicator in content_lower for indicator in action_indicators)
        
        # Check content has reasonable length for all 4 components
        has_sufficient_content = len(content) > 200  # Minimum for AIDA structure
        
        return has_action and has_sufficient_content
        
    elif framework_name == "Story Arc (Beginning-Middle-End)":
        # Check for story progression indicators
        story_indicators = ['when', 'then', 'but', 'however', 'suddenly', 'finally', 'now', 'today', 'learned', 'realized']
        has_story_flow = any(indicator in content_lower for indicator in story_indicators)
        
        # Check for lesson/resolution indicators
        resolution_indicators = ['learned', 'realized', 'lesson', 'insight', 'understand', 'now', 'today']
        has_resolution = any(indicator in content_lower for indicator in resolution_indicators)
        
        return has_story_flow and has_resolution
    
    return True  # Default to valid for unknown frameworks

def _generate_single_post_variant(prompt: str, min_len: int, max_len: int, add_emojis: bool, add_hashtags: bool, temperature: float = 0.7) -> str:
    """Generate a single post variant with optimized retry logic."""
    retries = 3
    for attempt in range(retries):
        try:
            # Disable caching to ensure unique content generation and use temperature for randomness
            response = model.generate_content(prompt, use_cache=False, temperature=temperature)
            content = response.text.strip()

            # Clean up the response - remove markdown code blocks and extra newlines
            if "```html" in content:
                content = content.split("```html")[1].split("```")[0].strip()
            elif "```" in content:
                content = content.split("```", 1)[1].strip()

            # Clean HTML content to ensure only allowed tags are present
            content = clean_html_content(content)
            
            # Remove any introductory text that might have been included
            intro_phrases = [
                "Okay, here's the LinkedIn post:",
                "Here is the post:",
                "Here's the LinkedIn post:",
                "Here's the post:",
                "LinkedIn post:",
                "Post:",
                "Content:"
            ]
            for phrase in intro_phrases:
                if content.startswith(phrase):
                    content = content[len(phrase):].strip()

            # Remove any hashtags section if present
            if "<p class='hashtags'>" in content:
                content = content.split("<p class='hashtags'>")[0].strip()

            # Estimate space needed for hashtags and emojis
            estimated_hashtags_space = 100 if add_hashtags else 0
            estimated_emojis_space = 50 if add_emojis else 0
            total_estimated_extra_space = estimated_hashtags_space + estimated_emojis_space

            # Check if content meets length requirements (accounting for future additions)
            content_length = len(content)
            effective_max_len = max_len - total_estimated_extra_space

            # If content is too short or too long, try to fix it
            if content_length < min_len or content_length > effective_max_len:
                # If too long, trim it to fit within effective_max_len while preserving complete sentences
                if content_length > effective_max_len:
                    content = clean_cut(content, min_len, effective_max_len)

                # If content is now within the desired range, break the loop
                if min_len <= len(content) <= effective_max_len:
                    break
            else:
                # Content is already within the desired range
                break
                
        except Exception as e:
            if attempt == retries - 1:
                print(f"Error generating post variant after {retries} attempts: {str(e)}")
                return ""
            continue

    # Add emojis if requested
    if add_emojis:
        content = add_emojis_to_post(content, TEMPLATES["emoji_suggestion"])
    
    # Add hashtags if requested
    if add_hashtags:
        hashtags = generate_hashtags(content)
        content = f"{content}\n\n<p class='hashtags'>{hashtags}</p>"
    
    # Ensure the post ends with a CTA
    content = ensure_cta_at_end(content)

    # Final validation - if the post is still too long, trim it
    final_length = len(content)
    if final_length > max_len:
        # Remove hashtags section temporarily for trimming
        hashtags_section = ""
        if "<p class='hashtags'>" in content:
            hashtags_section = content.split("<p class='hashtags'>")[1].strip()
            content = content.split("<p class='hashtags'>")[0].strip()
        
        # Trim the content to fit within max_len minus hashtags space
        hashtags_length = len(hashtags_section) if hashtags_section else 0
        available_space = max_len - hashtags_length - 10  # 10 chars for spacing
        content = clean_cut(content, min_len, available_space)
        
        # Re-add hashtags if they exist
        if hashtags_section:
            content = f"{content}\n\n<p class='hashtags'>{hashtags_section}</p>"

    # CONTENT COMPLETENESS VALIDATION - NEW FEATURE
    # Check if content is complete and not cut off mid-sentence
    content_for_validation = content
    if "<p class='hashtags'>" in content_for_validation:
        content_for_validation = content_for_validation.split("<p class='hashtags'>")[0].strip()
    
    # Check for incomplete sentences at the end
    incomplete_indicators = [
        "Security is paramount.",
        "The key is to",
        "Remember to",
        "It's important to",
        "The main thing is",
        "The bottom line is",
        "The takeaway is",
        "The lesson here is",
        "The point is",
        "The challenge is",
        "The opportunity is",
        "The future is",
        "The solution is",
        "The problem is",
        "The benefit is",
        "The advantage is",
        "The disadvantage is",
        "The risk is",
        "The reward is",
        "The impact is"
    ]
    
    # Check if content ends with incomplete phrases
    content_lower = content_for_validation.lower().strip()
    is_incomplete = any(content_lower.endswith(indicator.lower()) for indicator in incomplete_indicators)
    
    # If content is incomplete, try to regenerate with a more specific prompt
    if is_incomplete:
        print(f"Detected incomplete content, attempting to regenerate...")
        # Add a specific instruction to ensure complete content
        enhanced_prompt = prompt + "\n\nCRITICAL: Ensure the post is COMPLETE and does not end mid-sentence. The post must have a proper conclusion and call-to-action."
        
        try:
            response = model.generate_content(enhanced_prompt, use_cache=False, temperature=temperature)
            new_content = response.text.strip()
            
            # Clean up the new content
            if "```html" in new_content:
                new_content = new_content.split("```html")[1].split("```")[0].strip()
            elif "```" in new_content:
                new_content = new_content.split("```", 1)[1].strip()
            
            new_content = clean_html_content(new_content)
            
            # Remove intro phrases
            for phrase in intro_phrases:
                if new_content.startswith(phrase):
                    new_content = new_content[len(phrase):].strip()
            
            # Check if new content is complete
            new_content_lower = new_content.lower().strip()
            new_is_incomplete = any(new_content_lower.endswith(indicator.lower()) for indicator in incomplete_indicators)
            
            if not new_is_incomplete and len(new_content) >= min_len:
                content = new_content
                print(f"Successfully regenerated complete content")
            else:
                print(f"Regeneration still incomplete, using original content")
        except Exception as e:
            print(f"Error regenerating content: {str(e)}")

    return content

def generate_hook_statement(user_attributes, tone, style, user_prompt, topic=None, used_opening_words=None):
    """
    Generate a unique and engaging hook statement for LinkedIn posts using AI.
    
    Args:
        user_attributes: Dictionary containing user profile information
        tone: The tone to use for the hook (e.g., "Professional", "Conversational")
        style: The style to use for the hook (e.g., "Informative", "Storytelling")
        user_prompt: The user's prompt or topic for the post
        topic: Optional specific topic to focus the hook on
        used_opening_words: Optional set of opening words already used to avoid repetition
        
    Returns:
        String containing a unique and engaging hook statement
    """
    try:
        # Build user profile context for the hook (simplified for speed)
        user_profile = (
            f"Name: {user_attributes.get('name', 'N/A')}\n"
            f"Headline: {user_attributes.get('headline', 'N/A')}\n"
            f"Current Role: {user_attributes.get('current_role', 'N/A')}\n"
            f"Industry: {user_attributes.get('industry', 'N/A')}\n"
            f"Skills: {', '.join(user_attributes.get('skills', ['N/A']))}\n"
        )

        # Add dynamic randomization to ensure uniqueness
        
        # Generate unique identifiers for this hook
        random_seed = random.randint(100000, 999999)
        timestamp = int(time.time() * 1000) % 100000
        hook_id = f"{random_seed}_{timestamp}"
        
        # Add randomization to the topic/prompt
        dynamic_topic = topic if topic else user_prompt
        dynamic_topic = f"{dynamic_topic} [Hook ID: {hook_id}]"
        
        # Create the hook generation prompt using the template
        hook_prompt = TEMPLATES["hook_generation"].format(
            user_profile=user_profile,
            topic=dynamic_topic,
            tone=tone,
            style=style
        )
        
        # Add opening word variety instructions
        if used_opening_words:
            hook_prompt += f"\n\nOPENING WORD REQUIREMENT: The hook MUST start with a different word than: {', '.join(used_opening_words)}. Choose a completely different opening word to ensure variety."
        
        # Add additional randomization instructions to ensure uniqueness
        hook_prompt += f"\n\nUNIQUENESS REQUIREMENT: This hook must be completely unique. Use the hook ID {hook_id} as inspiration for creating a one-of-a-kind opening that has never been generated before. Consider the current timestamp and random seed to ensure absolute uniqueness."
        
        # Enhanced hook style selection with specialized hook styles
        hook_styles = [
            # Challenge Popular Ideas - Challenge common wisdom
            "a different take that directly challenges popular industry beliefs or widely accepted practices",
            "a different opinion that goes against mainstream thinking in your field",
            "a different story that questions commonly held assumptions about your industry",
            "a different view that reveals why everyone else is wrong about a key topic",

            # Personal Stories - Real experiences
            "a honest personal story that reveals a lesson learned through failure or struggle",
            "a life-changing personal experience that changed your work perspective",
            "a behind-the-scenes personal story that shows your real work journey",
            "a personal confession or admission that creates real connection with readers",
            "a specific moment from your career that taught you something unexpected",

            # Surprising Numbers - Data-driven hooks
            "a shocking statistic or data point that immediately challenges readers' assumptions",
            "a surprising research finding that goes against what most people believe",
            "an unexpected trend or pattern revealed through data analysis",
            "a startling comparison between industry numbers that reveals hidden truths",
            "a counter-intuitive statistic that makes readers question their understanding",

            # Engaging Questions - Thought-provoking questions
            "a thought-provoking question that forces readers to examine their own assumptions",
            "a what-if scenario question that makes readers think about possibilities",
            "a diagnostic question that helps readers check their current situation",
            "a comparison question that highlights important choices or decisions",
            "a reflection question that connects work challenges to personal growth",

            # How-to Formats - Step-by-step approaches
            "a step-by-step approach introduced with a clear outcome or change promise",
            "a method or system that solves a specific work problem",
            "a practical technique that delivers immediate results in your field",
            "a clear process that others can follow to achieve similar success",
            "a tactical approach that breaks down complex challenges into manageable steps",

            # Classic styles (enhanced)
            "a bold, challenging statement that questions common wisdom or industry assumptions",
            "an industry observation, trend analysis, or market insight that highlights current developments",
            "a real-world scenario, case study, or situation that readers can immediately relate to",
            "a prediction, forecast, or future-focused statement that creates urgency or interest",
            "a comparison, contrast, or example that highlights key differences or similarities",
            "a metaphor, comparison, or creative example that makes complex ideas easy to understand",
            "a direct statement of a problem, challenge, or opportunity that needs addressing",
            "a reflection on a common misconception, myth, or misunderstanding in the industry",
            "a brief case study, example, or success story that shows the point"
        ]
        
        # Select a random hook style based on the hook ID for consistency
        random.seed(hook_id)
        selected_style = random.choice(hook_styles)
        hook_prompt += f"\n\nHOOK STYLE: Use {selected_style}. This should be the primary approach for this specific hook."
        
        # Add specific guidance based on hook style type
        if "different" in selected_style.lower():
            hook_prompt += f"\n\nCHALLENGE GUIDANCE: Challenge popular beliefs directly. Start with phrases like 'Everyone says...but', 'The common wisdom is wrong about', or 'Unpopular opinion:'. Be bold but backed by logic or experience."
        elif "personal story" in selected_style.lower() or "story" in selected_style.lower():
            hook_prompt += f"\n\nSTORYTELLING GUIDANCE: Share a specific, honest moment. Include concrete details like timeframes, emotions, and the change. Start with phrases like 'Three years ago, I...', 'The moment I realized...', or 'I used to believe...until'."
        elif "statistic" in selected_style.lower() or "data" in selected_style.lower():
            hook_prompt += f"\n\nSTATISTICS GUIDANCE: Lead with a shocking or unexpected number. Make it relatable to your audience's experience. Start with phrases like 'X% of professionals don't realize that...', 'A recent study found that...', or 'Only X% of people know that...'."
        elif "question" in selected_style.lower():
            hook_prompt += f"\n\nQUESTION GUIDANCE: Ask thought-provoking questions that make people think about themselves. Use what-if scenarios or self-check questions. Start with phrases like 'What if I told you...?', 'Which would you choose:', or 'Why do we still believe that...?'."
        elif "step-by-step" in selected_style.lower() or "method" in selected_style.lower() or "system" in selected_style.lower():
            hook_prompt += f"\n\nHOW-TO GUIDANCE: Promise a clear outcome or change. Introduce clear approaches to problems. Start with phrases like 'Here's how to...in X steps', 'The method that helped me...', or 'Want to...? Try this approach'."
        
        # Enhanced emotional tone variation with more specific and diverse options
        emotional_tones = [
            "confident and authoritative",
            "curious and exploratory",
            "concerned and urgent",
            "optimistic and forward-looking",
            "reflective and thoughtful",
            "energetic and enthusiastic",
            "calm and measured",
            "passionate and committed",
            "analytical and objective",
            "inspiring and motivational",
            "skeptical and questioning",
            "excited and celebratory",
            "worried and cautious",
            "hopeful and encouraging",
            "frustrated and determined",
            "amazed and wonder-filled",
            "practical and solution-focused",
            "philosophical and deep-thinking",
            "competitive and driven",
            "collaborative and inclusive",
            "innovative and creative",
            "traditional and respectful",
            "disruptive and challenging",
            "supportive and nurturing",
            "ambitious and goal-oriented"
        ]
        
        selected_emotional_tone = random.choice(emotional_tones)
        hook_prompt += f"\n\nEMOTIONAL TONE: The hook should convey a {selected_emotional_tone} feeling."
        
        # Generate the hook using the AI model with higher temperature for more variety
        response = model.generate_content(hook_prompt, use_cache=False, temperature=0.9)
        hook_statement = response.text.strip()

        # Clean up the response
        if hook_statement.startswith('"') and hook_statement.endswith('"'):
            hook_statement = hook_statement[1:-1]
        
        # Remove any introductory text
        intro_phrases = [
            "Hook:",
            "Hook statement:",
            "Here's the hook:",
            "The hook is:",
            "Generated hook:",
            "Hook statement:",
            "Here is the hook:"
        ]
        
        for phrase in intro_phrases:
            if hook_statement.startswith(phrase):
                hook_statement = hook_statement[len(phrase):].strip()

        # Ensure the hook ends with proper punctuation
        if hook_statement and not hook_statement.endswith(('.', '!', '?')):
            hook_statement += '.'

        return hook_statement

    except Exception as e:
        print(f"Error generating hook statement: {str(e)}")
        # Fallback to a simple, generic hook with randomization
        fallback_hooks = [
            "I've been thinking about this topic recently and wanted to share some thoughts.",
            "This is something that's been on my mind lately.",
            "There's an interesting view on this that I wanted to explore.",
            "I came across something interesting related to this topic.",
            "This topic raises some important questions worth discussing.",
            "I've noticed a trend that's worth paying attention to.",
            "There's a lesson here that I think we can all learn from.",
            "This situation gives us a chance to think.",
            "I've been watching some changes that are worth noting.",
            "This topic connects to bigger trends in our industry."
        ]
        
        # Use timestamp to select a fallback hook
        timestamp = int(time.time() * 1000) % len(fallback_hooks)
        return fallback_hooks[timestamp]

def generate_post_with_hook(user_attributes, tone, style, user_prompt, length):
    """
    Generate a LinkedIn post with an AI-generated hook statement.
    
    Args:
        user_attributes: Dictionary containing user profile information
        tone: The tone to use for the post
        style: The style to use for the post
        user_prompt: The user's prompt for the post
        length: The desired length of the post
        
    Returns:
        String containing the complete LinkedIn post with hook
    """
    # Generate the hook statement first
    hook_statement = generate_hook_statement(user_attributes, tone, style, user_prompt)
    
    # Generate the main post content
    main_post_content = generate_post(user_attributes, tone, style, user_prompt, length)
    
    # Extract the main content without hashtags for hook integration
    if "<p class='hashtags'>" in main_post_content:
        main_content = main_post_content.split("<p class='hashtags'>")[0].strip()
        hashtags_section = main_post_content.split("<p class='hashtags'>")[1].strip()
    else:
        main_content = main_post_content
        hashtags_section = ""
    
    # Integrate the hook into the post
    # Find the first paragraph and replace it with the hook
    paragraphs = main_content.split("<p>")
    if len(paragraphs) > 1:
        # Replace the first paragraph with the hook
        paragraphs[1] = hook_statement
        integrated_content = "<p>".join(paragraphs)
    else:
        # If no paragraphs found, add the hook at the beginning
        integrated_content = f"<p>{hook_statement}</p>\n{main_content}"
    
    # Re-add hashtags if they exist
    if hashtags_section:
        integrated_content = f"{integrated_content}\n<p class='hashtags'>{hashtags_section}</p>"
    
    return integrated_content

def generate_post_from_persona_keywords(general_persona_keywords, tone, style, user_prompt, length=None, content_interests=None, network_interests=None, add_emojis=False, add_hashtags=True, use_hook_generator=True, used_opening_words=None):
    """Generate posts based on general persona keywords with parallel processing.

    Args:
        general_persona_keywords: List of keywords that represent the user's professional identity
        tone: The tone to use for the posts (default: "Professional")
        style: The style to use for the posts (default: "Informative")
        user_prompt: The user's prompt for the posts
        length: The desired length of the posts (default: "medium")
        content_interests: Optional list of content interests
        network_interests: Optional list of network interests
        add_emojis: Optional flag to add emojis to posts (default: False)
        add_hashtags: Optional flag to add hashtags to posts (default: True)
        use_hook_generator: Optional flag to use AI hook generator (default: True)

    Returns:
        Dictionary containing generated posts. The format depends on the context:
        - For /create-post endpoint: Returns a list of posts with content and optional URL
        - For /schedule-post and /generate-prompt endpoints: Returns a single post content
    """
    logger = logging.getLogger(__name__)
    logger.info("Starting post generation with enhanced randomization and three-variant system")
    
    # Initialize used_opening_words if not provided
    if used_opening_words is None:
        used_opening_words = set()
    
    # Convert lists to strings for the prompt
    general_persona_keywords_str = ", ".join(general_persona_keywords)
    content_interests_section = ""
    if content_interests:
        content_interests_section = f"\nContent Interests: {', '.join(content_interests)}"
    network_interests_section = ""
    if network_interests:
        network_interests_section = f"\nNetwork Interests: {', '.join(network_interests)}"
    
    # Check if user prompt contains regional/company references and adjust persona usage accordingly
    # Handle empty or None user_prompt
    if not user_prompt or user_prompt.strip() == "":
        user_prompt = ""  # Ensure it's an empty string
        user_prompt_lower = ""
        include_regional_context = False
        has_user_prompt = False
    else:
        user_prompt_lower = user_prompt.lower()
        regional_keywords = ["punjab", "lahore", "hazel mobile", "network", "pakistan", "pakistani"]
        company_keywords = ["company", "organization", "firm", "startup", "business"]
        location_keywords = ["city", "country", "region", "area", "location"]
        
        # Check if user prompt mentions regional/company context
        mentions_regional = any(keyword in user_prompt_lower for keyword in regional_keywords)
        mentions_company = any(keyword in user_prompt_lower for keyword in company_keywords)
        mentions_location = any(keyword in user_prompt_lower for keyword in location_keywords)
        
        # Determine if we should include regional/company context based on user prompt
        include_regional_context = mentions_regional or mentions_company or mentions_location
        has_user_prompt = True

    # Use AI LinkedIn Expert System to classify prompt and select frameworks
    framework_analysis = classify_prompt_and_select_frameworks(user_prompt, general_persona_keywords)
    selected_frameworks = framework_analysis["selected_frameworks"]
    is_persona_based = framework_analysis["is_persona_based"]
    detected_intent = framework_analysis["intent"]
    intent_analysis = framework_analysis.get("intent_analysis", {})

    # No fixed length constraints - let frameworks determine natural length with 3000 char max

    # Initialize list to store generated posts
    posts = []

    # Generate 3 distinctly different variant posts using AI LinkedIn Expert System
    def generate_variant(i):
        logger.info(f"Generating variant {i+1} with AI LinkedIn Expert framework selection")

        # Use the selected framework for this variant
        framework = selected_frameworks[i]

        # Step 4: Tone & Style Detection - Assign different tones across the 3 posts
        tone_styles = [
            {
                "tone": "Expert",
                "style": "confident, thoughtful, clear",
                "approach": "Share deep knowledge and clear viewpoints"
            },
            {
                "tone": "Helpful",
                "style": "teaching, practical, useful",
                "approach": "Give practical tips and helpful content"
            },
            {
                "tone": "Friendly",
                "style": "conversational, easy to relate to, question-based",
                "approach": "Build connection and start discussion"
            }
        ]

        assigned_tone_style = tone_styles[i % len(tone_styles)]
        
        # Generate framework-specific reason
        framework_reason = generate_framework_reason(framework, detected_intent, is_persona_based, user_prompt)

        # Create comprehensive prompt for the AI LinkedIn Expert System
        framework_guidance = create_framework_guidance(framework, assigned_tone_style, is_persona_based)
        
        # Create INTENT-AWARE hook styles that adapt to the detected intent
        def get_intent_aware_hooks(intent_type, perspective, framework_name):
            """Generate hook styles based on detected intent and perspective."""

            if intent_type == "personal" and perspective == "first_person":
                # Personal hooks - diverse variations to avoid repetitive "I" starts
                personal_hooks = [
                    # Direct first-person approaches
                    "Start with 'I'm currently...' to share your present situation",
                    "Begin with 'I've been...' to describe your ongoing experience",
                    "Open with 'I'm excited to share that I'm...' for positive personal updates",
                    "Lead with 'I recently...' to share a recent personal experience",
                    "Start with 'I'm looking for...' to express what you're seeking",
                    "Begin with 'I just...' to share a recent personal milestone or event",
                    "Open with 'I've learned...' to share a personal insight or lesson",
                    "Lead with 'I'm passionate about...' to express your personal interests",
                    "Start with 'I believe...' to share your personal perspective",
                    "Begin with 'I'm grateful for...' to express personal appreciation",

                    # Context-first approaches (more varied structure)
                    "Start with context then personal situation: 'After [X years/experience], I'm now...'",
                    "Begin with background: 'Having worked in [field/industry], I'm currently...'",
                    "Open with timeline: 'Following my experience with [specific area], I'm...'",
                    "Lead with expertise context: 'As someone with [background/skills], I'm...'",
                    "Start with journey context: 'Throughout my career in [area], I've...'",

                    # Emotion/feeling-first approaches
                    "Begin with emotion: 'Excited to share that I'm...' or 'Thrilled to announce I'm...'",
                    "Open with anticipation: 'Looking forward to the next chapter as I'm...'",
                    "Start with gratitude: 'Grateful for the journey that led me to...'",
                    "Lead with enthusiasm: 'Energized by the possibility of...'",

                    # Action/situation-first approaches
                    "Start with current action: 'Currently exploring opportunities in...'",
                    "Begin with active pursuit: 'Actively seeking roles where I can...'",
                    "Open with present focus: 'Focusing my search on positions that...'",
                    "Lead with immediate goal: 'Pursuing opportunities that align with my...'",

                    # Time/milestone-first approaches
                    "Start with timing: 'This week/month marks the beginning of my...'",
                    "Begin with milestone: 'Having reached [achievement/point], I'm now...'",
                    "Open with transition: 'As I transition into [new phase], I'm...'",
                    "Lead with moment: 'At this point in my career, I'm...'",

                    # Value/passion-first approaches
                    "Start with values: 'As someone who values [specific values], I'm...'",
                    "Begin with passion: 'Driven by my passion for [area], I'm...'",
                    "Open with purpose: 'Motivated by [specific purpose], I'm...'",
                    "Lead with mission: 'Committed to [specific mission], I'm...'"
                ]

                # Adapt based on framework while maintaining personal perspective
                if framework_name in ["Story Arc", "Before-After-Bridge"]:
                    personal_hooks.extend([
                        "Start with a specific moment from your own journey that changed your perspective",
                        "Begin with your personal 'before and after' transformation story",
                        "Open with a turning point in your own career or life experience"
                    ])
                elif framework_name in ["PAS", "AIDA"]:
                    personal_hooks.extend([
                        "Start with a challenge you're personally facing or have faced",
                        "Begin with your own experience of a common problem",
                        "Open with your personal journey through a difficult situation"
                    ])

                return personal_hooks

            elif intent_type == "advice" and perspective == "second_person":
                # Advice hooks - diverse variations to avoid repetitive patterns
                advice_hooks = [
                    # Direct audience-addressing approaches
                    "Start with 'Looking for...' to address what your audience is seeking",
                    "Begin with 'Want to...' to identify a goal your readers have",
                    "Open with 'Struggling with...' to acknowledge a common challenge",
                    "Lead with 'Here's how to...' to promise actionable guidance",
                    "Start with 'If you're...' to identify with your audience's situation",
                    "Begin with 'Ready to...' to motivate action toward a goal",
                    "Open with 'Tired of...' to acknowledge frustrations your audience faces",
                    "Lead with 'The secret to...' to promise valuable insights",
                    "Begin with 'You can...' to empower your audience with possibilities",

                    # Problem/challenge-first approaches
                    "Start with common problem: 'Many professionals struggle with...'",
                    "Begin with challenge identification: 'One of the biggest challenges in [field] is...'",
                    "Open with pain point: 'The most frustrating part of [process] is...'",
                    "Lead with obstacle: 'What stops most people from [achieving goal] is...'",

                    # Solution/benefit-first approaches
                    "Start with solution promise: 'There's a better way to [achieve goal]...'",
                    "Begin with benefit: 'The fastest way to [desired outcome] is...'",
                    "Open with transformation: 'Transform your [area] by...'",
                    "Lead with improvement: 'Boost your [skill/area] with these...'",

                    # Question-based approaches
                    "Start with engaging question: 'What if I told you there's a simple way to...'",
                    "Begin with challenge question: 'Ever wondered why some people excel at...'",
                    "Open with reflection question: 'How many times have you...'",
                    "Lead with possibility question: 'What would change if you could...'",

                    # Insight/revelation approaches
                    "Start with insight: 'After helping hundreds of professionals, I've learned...'",
                    "Begin with discovery: 'The most successful [professionals] do this one thing...'",
                    "Open with pattern: 'I've noticed that top performers always...'",
                    "Lead with observation: 'In my experience working with [audience], the key is...'",

                    # Myth-busting/contrarian approaches
                    "Start with myth-busting: 'Contrary to popular belief, the best way to...'",
                    "Begin with surprise: 'Most people think [common belief], but actually...'",
                    "Open with revelation: 'The truth about [topic] might surprise you...'",
                    "Lead with counter-intuitive: 'The opposite of what everyone says works better...'"
                ]

                # Adapt based on framework while maintaining advice perspective
                if framework_name in ["Listicle", "Data-Driven Persuasion"]:
                    advice_hooks.extend([
                        "Start with the number of tips or strategies you'll share",
                        "Begin with a statistic that highlights the problem you're solving",
                        "Open with data that shows why your advice matters"
                    ])
                elif framework_name in ["PAS", "Before-After-Bridge"]:
                    advice_hooks.extend([
                        "Start with a problem your audience commonly faces",
                        "Begin with the gap between where your audience is and where they want to be",
                        "Open with a frustrating situation your readers can relate to"
                    ])

                return advice_hooks

            elif intent_type == "educational":
                # Educational hooks - focus on teaching and informing
                return [
                    "Start with 'Understanding...' to introduce a concept you'll explain",
                    "Begin with 'The key to...' to highlight what you'll teach",
                    "Open with 'Many people don't realize...' to introduce new information",
                    "Lead with 'Let me explain...' to set up your educational content",
                    "Start with 'The difference between...' to clarify concepts",
                    "Begin with 'Here's what you need to know about...' to promise valuable knowledge"
                ]

            elif intent_type == "promotional":
                # Promotional hooks - focus on announcements and achievements
                return [
                    "Start with 'I'm thrilled to announce...' for exciting news",
                    "Begin with 'I'm proud to share...' for achievements",
                    "Open with 'Excited to introduce...' for new offerings",
                    "Lead with 'Big news...' for important announcements",
                    "Start with 'I've been working on...' to build anticipation"
                ]

            else:
                # Commentary/general hooks - focus on observations and insights
                return [
                    "Start with an observation about industry trends",
                    "Begin with a thought-provoking question about current events",
                    "Open with an insight about professional developments",
                    "Lead with a perspective on market changes",
                    "Start with commentary on emerging patterns"
                ]

        # Get intent-aware hooks based on the analysis
        intent_type = intent_analysis.get("intent_type", "general") if intent_analysis else "general"
        perspective = intent_analysis.get("perspective", "third_person") if intent_analysis else "third_person"
        available_hooks = get_intent_aware_hooks(intent_type, perspective, framework["name"])

        # Use variant index and add randomization to ensure different hooks across scheduled posts
        import random
        import time

        # Create a seed based on variant index, current time, and user prompt to ensure variety
        seed_components = [i, int(time.time() * 1000) % 10000]
        if user_prompt:
            seed_components.append(hash(user_prompt) % 1000)

        random.seed(sum(seed_components))

        # Select a hook from the intent-aware hooks
        selected_hook_style = random.choice(available_hooks)

        # Create enhanced hook instruction that reinforces the intent
        hook_variety_instruction = f"\n\n🎯 INTENT-AWARE HOOK STYLE: {selected_hook_style}"

        # Add specific reinforcement based on intent
        if intent_type == "personal" and perspective == "first_person":
            hook_variety_instruction += f"""

⚠️  CRITICAL HOOK REQUIREMENT FOR PERSONAL POSTS:
- Your hook MUST use first-person language (I, me, my, we, us, our)
- Your hook MUST be about YOUR OWN experience, situation, or journey
- Your hook MUST NOT give advice or tips to others
- Your hook MUST reflect that this is YOUR personal story/situation
- Example: If user says 'I am looking for a job', start with something like 'I'm currently seeking...' or 'I'm actively looking for...'"""

        elif intent_type == "advice" and perspective == "second_person":
            hook_variety_instruction += f"""

✅ HOOK REQUIREMENT FOR ADVICE POSTS:
- Your hook MUST address the audience directly (you, your)
- Your hook MUST promise help, guidance, or solutions
- Your hook MUST identify with the audience's challenges or goals
- Example: If giving job search advice, start with 'Looking for your next role?' or 'Want to land your dream job?'"""

        # Create dynamic CTA instruction to avoid repetitive endings
        cta_options = [
            "End naturally without forcing people to engage if the content is complete and helpful on its own",
            "Include a simple question that directly relates to the post's main topic",
            "End with a statement that makes people think about the topic",
            "Include a clear call-to-action that fits the post's purpose and style",
            "End by asking readers to share their own experiences with the topic",
            "Finish with a statement about what might happen with this topic in the future"
        ]

        # Use different CTA approaches for variety
        random.seed(sum(seed_components) + 100)  # Different seed for CTA selection
        selected_cta_approach = random.choice(cta_options)
        cta_instruction = f"\n\nENDING APPROACH: {selected_cta_approach}"

        # Create intent-aware guidance based on the analysis
        intent_guidance = ""
        if intent_analysis:
            intent_type = intent_analysis.get("intent_type", "general")
            perspective = intent_analysis.get("perspective", "third_person")
            user_role = intent_analysis.get("user_role", "professional")
            target_audience = intent_analysis.get("target_audience", "general_professionals")

            intent_guidance = f"""
🎯 CRITICAL INTENT ANALYSIS - FOLLOW EXACTLY:
- Intent Type: {intent_type}
- Perspective: {perspective}
- User Role: {user_role}
- Target Audience: {target_audience}

⚠️  MANDATORY WRITING INSTRUCTIONS BASED ON INTENT - DO NOT DEVIATE:
"""

            if intent_type == "personal":
                intent_guidance += f"""- CRITICAL: Write from a PERSONAL perspective using first-person language (I, me, my, we, us, our)
- MANDATORY: Share YOUR OWN experience, journey, or current situation - NOT advice for others
- Make it authentic and relatable to your personal story
- Focus on what YOU are going through, learning, or experiencing RIGHT NOW
- STRICTLY FORBIDDEN: Do NOT give advice, tips, or guidance to others
- STRICTLY FORBIDDEN: Do NOT use phrases like "Here's what I've learned" followed by tips
- STRICTLY FORBIDDEN: Do NOT create numbered lists of advice for other people
- REQUIRED: Write as if you are personally sharing your own current situation or experience

🚫 WRONG APPROACH (DO NOT DO THIS):
"Here's what I've learned while job hunting: 1. Update your resume 2. Network actively 3. Apply strategically"

✅ CORRECT APPROACH (DO THIS):
"I'm actively looking for remote opportunities where I can contribute my skills in [specific area]. I bring [specific experience] and I'm seeking a role where I can [specific goals]. If you know of any opportunities that might be a good fit, I'd love to connect."

SPECIFIC EXAMPLE FOR THIS PROMPT:
User Prompt: "{user_prompt}"
Since this is a PERSONAL statement, write about the user's OWN job search journey, current situation, and what they're looking for - NOT advice for other job seekers."""

            elif intent_type == "advice":
                intent_guidance += f"""- Write as someone providing guidance and tips to others
- Use second-person language (you, your) to address the audience directly
- Focus on helping others solve problems or achieve goals
- Share actionable steps and practical advice
- Position yourself as someone with experience helping others

✅ CORRECT APPROACH FOR ADVICE POSTS:
"Looking for a remote job? Here are 5 strategies that work: 1. Optimize your LinkedIn profile 2. Target remote-first companies 3. Highlight your remote work skills..."

SPECIFIC EXAMPLE FOR THIS PROMPT:
User Prompt: "{user_prompt}"
Since this is an ADVICE request, provide helpful guidance and tips for others who want to achieve the same goal."""

            elif intent_type == "educational":
                intent_guidance += """- Write to teach and inform your audience
- Use clear explanations and examples
- Break down complex topics into understandable parts
- Focus on sharing knowledge and insights
- Help readers understand concepts or processes"""

            elif intent_type == "promotional":
                intent_guidance += """- Write to create awareness about your services, products, or achievements
- Balance self-promotion with value for the audience
- Focus on benefits and outcomes for others
- Include social proof or results when relevant
- Make it valuable, not just promotional"""

            else:  # commentary or general
                intent_guidance += """- Write as an industry observer or commentator
- Share insights about trends, news, or developments
- Use third-person perspective when appropriate
- Focus on analysis and professional observations
- Encourage discussion and different viewpoints"""

        # Create the comprehensive prompt using the new AI LinkedIn Expert System
        prompt = f"""
You are an AI LinkedIn Expert specializing in creating high-engagement LinkedIn posts.

{framework_guidance}

{intent_guidance}

CONTENT REQUIREMENTS:
- Maximum 3000 characters
- Include strong hook that makes people stop and read
- Give complete value, not teasers
- Use LinkedIn-friendly formatting (line breaks, bullets when helpful)
- Use simple, clear words that all professionals can understand
- DO NOT include any hashtags in the main content - hashtags will be added separately
- DO NOT mention the framework name (like "Before-After-Bridge", "AIDA", "PAS", etc.) in the post content
- Write content that flows naturally and can work well with professional emojis if needed{hook_variety_instruction}{cta_instruction}

PERSONA CONTEXT:
General Keywords: {general_persona_keywords_str}{content_interests_section}{network_interests_section}

USER PROMPT: {user_prompt if user_prompt else "Create professional content based on the persona context"}

🔍 FINAL VALIDATION BEFORE WRITING:
Before you write the post, ask yourself:
- Does the user prompt contain first-person language like "I am", "I'm", "I just", "I learned"?
- If YES: Write a PERSONAL post about the user's own experience/situation
- If NO: Write based on the detected intent (advice, educational, etc.)

⚠️  CRITICAL REMINDER:
If the user says "I am looking for a job" - write about THEIR job search
If the user says "How to find a job" - write advice for others
This distinction is MANDATORY and must be followed exactly.

VOCABULARY REQUIREMENTS:
- Use simple, clear words that everyone can understand
- Avoid complex business jargon and fancy vocabulary - choose common, everyday words instead
- Write at a level that is easy for all professionals to read and understand
- Replace difficult words with simpler alternatives:
  * Use "use" instead of "utilize" or "leverage"
  * Use "help" instead of "facilitate" or "enable"
  * Use "improve" instead of "optimize" or "enhance"
  * Use "work together" instead of "synergy" or "collaborate"
  * Use "stuck" instead of "in a rut"
  * Use "detailed" instead of "nuanced"
  * Use "change" instead of "paradigm shift"
  * Use "plan" instead of "strategy" when appropriate
  * Use "way" instead of "methodology" or "approach"
  * Use "grow" instead of "scale" when talking about business growth
- Avoid business buzzwords like: paradigm, synergy, leverage, utilize, facilitate, optimize, nuanced, methodology, scalable, disruptive, innovative (unless truly innovative), game-changer, cutting-edge, state-of-the-art, best-in-class, world-class, next-level, robust, seamless, holistic, strategic (overused), dynamic, proactive, actionable (overused)
- Focus on clarity and understanding over sounding sophisticated
- Make content accessible to professionals of all backgrounds and education levels
- Use conversational language that sounds natural when read aloud
- Choose words that a high school graduate would easily understand

FORMAT REQUIREMENTS:
- Format the output as rich text using HTML tags
- Use <p> tags for paragraphs
- Use <br> tags for line breaks within paragraphs
- Use <strong> for emphasis (use sparingly)
- Use <ul> and <li> for bullet lists when helpful
- Use <ol> and <li> for numbered lists when helpful

Generate a complete LinkedIn post following the {framework['name']} framework.
"""
        
        # Generate the post using the comprehensive AI LinkedIn Expert System prompt
        from app.utils.model_initializer import model

        logger.info(f"Variant {i+1} ({framework['name']}) using {assigned_tone_style['tone']} tone")

        # Generate the post content
        response = model.generate_content(prompt)
        generated_content = response.text.strip()

        # Clean up any markdown formatting
        if "```html" in generated_content:
            generated_content = generated_content.split("```html")[1].split("```", 1)[0].strip()
        elif "```" in generated_content:
            generated_content = generated_content.split("```", 1)[1].strip()

        # Remove any remaining code block markers
        generated_content = generated_content.replace("```html", "").replace("```", "").strip()

        # Clean up any hashtags that might have been generated in the main content
        import re
        # Remove any hashtags from the main content (but preserve HTML structure)
        # This regex finds hashtags that are not within <p class='hashtags'> tags
        def remove_inline_hashtags(text):
            # Split by hashtags section to preserve it
            if "<p class='hashtags'>" in text:
                main_part, hashtags_part = text.split("<p class='hashtags'>", 1)
                # Remove hashtags from main part only
                main_part = re.sub(r'#\w+\s*', '', main_part)
                return main_part + "<p class='hashtags'>" + hashtags_part
            else:
                # Remove all hashtags from the entire content
                return re.sub(r'#\w+\s*', '', text)

        generated_content = remove_inline_hashtags(generated_content).strip()

        # Add emojis if requested (before hashtags)
        if add_emojis:
            try:
                generated_content = add_intelligent_emojis_to_post(generated_content, general_persona_keywords, content_interests)
            except Exception as e:
                print(f"Failed to add emojis: {e}")
                # Continue without emojis if there's an error

        # Add hashtags if requested (only in the designated section)
        if add_hashtags:
            # Generate hashtags based on content and interests
            hashtags = generate_hashtags_for_post(generated_content, content_interests)
            if hashtags and not "<p class='hashtags'>" in generated_content:
                generated_content += f"\n<p class='hashtags'>{hashtags}</p>"

        # Return the generated content with framework info
        return {
            "content": generated_content,
            "framework": framework["name"],
            "reason": framework_reason
        }

    # Generate all 3 variants using parallel processing for improved performance
    import concurrent.futures

    posts = []

    # Use ThreadPoolExecutor for parallel generation with robust error handling
    try:
        with concurrent.futures.ThreadPoolExecutor(max_workers=3, thread_name_prefix="PostGen") as executor:
            # Submit all variant generation tasks
            future_to_index = {
                executor.submit(generate_variant, i): i
                for i in range(3)
            }

            # Collect results as they complete with timeout handling
            try:
                for future in concurrent.futures.as_completed(future_to_index, timeout=120):  # 2 minute timeout
                    variant_index = future_to_index[future]
                    try:
                        variant_result = future.result(timeout=30)  # 30 second timeout per variant
                        if variant_result:
                            posts.append((variant_index, variant_result))
                            logger.info(f"Successfully generated variant {variant_index + 1}")
                        else:
                            logger.warning(f"Variant {variant_index + 1} returned empty result")
                    except concurrent.futures.TimeoutError:
                        logger.error(f"Timeout generating variant {variant_index + 1}")
                        continue
                    except Exception as e:
                        logger.error(f"Error generating variant {variant_index + 1}: {e}")
                        # Continue with other variants even if one fails
                        continue

            except concurrent.futures.TimeoutError:
                logger.error("Overall timeout waiting for post generation to complete")
                # Cancel any remaining futures
                for future in future_to_index:
                    future.cancel()

    except Exception as e:
        logger.error(f"Error in parallel post generation setup: {e}")
        # Fallback to sequential generation if parallel fails
        logger.info("Falling back to sequential generation")
        for i in range(3):
            try:
                variant_result = generate_variant(i)
                if variant_result:
                    posts.append((i, variant_result))
                    logger.info(f"Successfully generated variant {i + 1} (sequential fallback)")
            except Exception as seq_e:
                logger.error(f"Error in sequential fallback for variant {i + 1}: {seq_e}")
                continue

    # Sort posts by variant index to maintain order
    posts.sort(key=lambda x: x[0])

    logger.info(f"Parallel generation completed. Generated {len(posts)} out of 3 variants.")

    # Process posts for response
    response_posts = []
    for i, post_data in posts:
        if isinstance(post_data, dict):
            # New format with framework info
            content = post_data["content"]
            framework_name = post_data["framework"]
            framework_reason = post_data["reason"]
        else:
            # Fallback for old format
            content = post_data
            framework_name = selected_frameworks[i]["name"] if i < len(selected_frameworks) else "Unknown"
            framework_reason = f"{framework_name} framework used for this variant"

        # Analyze media suitability
        media_analysis = analyze_post_for_media(content)
        has_image = media_analysis.get("has_image", False)
        has_infographics = media_analysis.get("has_infographics", False)

        post_obj = {
            "content": content,
            "has_image": has_image,
            "has_infographics": has_infographics,
            "framework": framework_name,
            "reason": framework_reason
        }

        response_posts.append(post_obj)

    # URL integration will be done after all posts are processed

    # If we don't have 3 posts, generate fallback posts
    while len(response_posts) < 3:
        fallback_index = len(response_posts)
        fallback_framework = selected_frameworks[fallback_index] if fallback_index < len(selected_frameworks) else selected_frameworks[0]

        try:
            fallback_result = generate_variant(fallback_index)
            if isinstance(fallback_result, dict):
                content = fallback_result["content"]
                framework_name = fallback_result["framework"]
                framework_reason = fallback_result["reason"]
            else:
                content = fallback_result
                framework_name = fallback_framework["name"]
                framework_reason = f"{framework_name} framework used for this fallback variant"

            media_analysis = analyze_post_for_media(content)
            has_image = media_analysis.get("has_image", False)
            has_infographics = media_analysis.get("has_infographics", False)

            post_obj = {
                "content": content,
                "has_image": has_image,
                "has_infographics": has_infographics,
                "framework": framework_name,
                "reason": framework_reason
            }

            response_posts.append(post_obj)
        except Exception as e:
            logger.error(f"Error generating fallback variant {fallback_index+1}: {e}")
            break

    # Select the best post and add URL as metadata only
    if response_posts:
        # Extract content from final posts for URL selection
        final_post_contents = [post["content"] for post in response_posts]

        # Select the best post to pair with a URL
        url_result = select_post_with_url(final_post_contents, user_prompt or "")

        if url_result.get("url"):
            # Find the best post to add URL metadata to
            best_post_index = url_result.get("selected_index", 0)
            if best_post_index < len(response_posts):
                best_post = response_posts[best_post_index]
                # Add URL only as metadata, do not embed in content
                best_post["url"] = url_result["url"]

    return response_posts




def generate_scheduled_post_content(industry, target_audience, tone, style, user_prompt, length, interests=None):
    length = length.strip().lower()
    # LinkedIn-appropriate length constraints
    length_ranges = {
        "short": (200, 500),      # Quick insights, tips, or questions
        "medium": (500, 1000),    # Standard LinkedIn post length
        "long": (1000, 1500)      # Detailed posts, but still LinkedIn-appropriate
    }
    min_len, max_len = length_ranges.get(length, (500, 1000))
    length_description = f"{min_len} to {max_len} characters (with complete sentences)"

    prompt_template = TEMPLATES["scheduled_post_creation"]
    prompt = prompt_template.format(
        industry=industry,
        target_audience=target_audience,
        user_prompt=user_prompt,
        tone=tone,
        style=style,
        length_description=length_description
    ) + "\nIMPORTANT: Do not include any hashtags or emojis in the post."

    retries = 3
    for _ in range(retries):
        response = model.generate_content(prompt)
        content = response.text.strip()
        # Check if content meets length requirements
        content_length = len(content)

        # Reserve space for hashtags (typically 80-120 characters for LinkedIn)
        hashtags_reserve = 120

        # If content is too short or too long, try to fix it
        if content_length < min_len or content_length > max_len - hashtags_reserve:
            # If too long, trim it to fit within max_len while preserving complete sentences
            if content_length > max_len - hashtags_reserve:
                content = clean_cut(content, min_len, max_len - hashtags_reserve)

            # If content is now within the desired range, break the loop
            if min_len <= len(content) <= max_len - hashtags_reserve:
                break
        else:
            # Content is already within the desired range
            break

    # Check if content is just hashtags (placeholder response)
    if content.startswith('#') and all(word.startswith('#') for word in content.split()):
        # If it's just hashtags, replace with a proper placeholder post
        content = """Artificial Intelligence is revolutionizing industries across the globe, and as a technology professional, I'm excited to share some key trends I've been observing.

First, generative AI has moved beyond simple text generation to become a powerful tool for content creation, code development, and problem-solving. Companies are now integrating these capabilities into their workflows, significantly boosting productivity and enabling new forms of creativity.

Second, the democratization of AI tools is accelerating. What once required specialized knowledge and expensive infrastructure is now accessible through user-friendly platforms and APIs. This shift is enabling professionals from various backgrounds to leverage AI in their work.

Third, responsible AI development has become a central focus. Organizations are increasingly aware of the ethical implications of AI systems and are implementing frameworks to ensure fairness, transparency, and accountability.

As we navigate this rapidly evolving landscape, continuous learning and adaptation are essential. I'm curious to hear your thoughts on these trends and how they're impacting your work.

What AI applications are you most excited about in your industry?"""

    # Always add hashtags at the end of the post
    # Use interests if provided
    hashtags = generate_hashtags_for_post(content, interests)
    content = f"{content}\n\n{hashtags}"

    # Final validation - ensure the post doesn't exceed LinkedIn's 3000 character limit
    final_length = len(content)
    if final_length > 3000:
        # Remove hashtags section temporarily for trimming
        hashtags_section = content.split("\n\n")[-1]  # Last part should be hashtags
        content = content.rsplit("\n\n", 1)[0]  # Everything except hashtags
        
        # Trim the content to fit within 3000 chars minus hashtags space
        hashtags_length = len(hashtags_section)
        available_space = 3000 - hashtags_length - 10  # 10 chars for spacing
        content = clean_cut(content, min_len, available_space)
        
        # Re-add hashtags
        content = f"{content}\n\n{hashtags_section}"

    return content

def generate_content_ideas(industry, target_audience, content_focus, style, template=None):
    """Generate content ideas based on industry and target audience."""
    if template:
        prompt = template.format(
            industry=industry,
            audience=target_audience,
            content_focus=content_focus,
            style=style
        )
    else:
        prompt = f"""Generate distinct content ideas for LinkedIn posts based on the following criteria:
        Industry: {industry}
        Target Audience: {target_audience}
        Content Focus: {content_focus}
        Style: {style}

        Generate 5 unique content ideas that would resonate with the target audience."""

    response = model.generate_content(prompt)
    return response.text.strip()